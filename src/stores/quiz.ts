/* eslint-disable @typescript-eslint/no-redundant-type-constituents */

// ! need to fix type, refactor this file
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/require-await */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Assessment } from 'src/types/models';
// import type { DataResponse } from 'src/types/data';
import { AssessmentService } from 'src/services/asm/assessmentService';

// Fake services for now
const quizAttemptService = {
  startQuizAttempt: async () => ({ data: {} }),
  navigateToQuestion: async () => ({ data: { question: {}, attemptAnswer: {} } }),
  getActiveQuizAttempt: async () => ({ data: {} }),
  saveAnswer: async () => {},
  submitQuizAttempt: async () => ({ data: {} }),
};
const quizDashboardService = {
  getQuizSummary: async () => ({}),
  getUserAttempts: async () => ({}),
  getQuizAnswerSummary: async () => ({}),
};
import { useQuasar } from 'quasar';

export const useQuizStore = defineStore('quiz', () => {
  // --- Attempt State ---
  const currentAttempt = ref<any>(null);
  const currentQuestion = ref<any>(null);
  const currentAttemptAnswer = ref<any>(null);
  const submitResult = ref<any>(null);
  const loading = ref(false);

  // --- CRUD State ---
  const quizzes = ref<Assessment[]>([]);
  const currentAssessment = ref<Assessment | null>(null);

  // --- Dashboard State ---
  const $q = useQuasar();
  const currentQuizId = ref<number | null>(null);
  const quizSummary = ref<MappedQuizSummary | null>(null);
  const userAttempts = ref<any>(null);
  const answerSummary = ref<any>(null);
  const allResponsesData = ref<QuizAllResponsesData | null>(null);

  const isLoadingAllResponses = ref(false);
  const isLoadingSummary = ref(false);
  const isLoadingUserAttempts = ref(false);
  const isLoadingAnswerSummary = ref(false);

  const errorAllResponses = ref<string | null>(null);
  const errorSummary = ref<string | null>(null);
  const errorUserAttempts = ref<string | null>(null);
  const errorAnswerSummary = ref<string | null>(null);

  // --- Attempt Actions ---
  const hasQuizData = computed(
    () =>
      !!quizSummary.value ||
      !!allResponsesData.value ||
      !!userAttempts.value ||
      !!answerSummary.value,
  );

  const overallLoading = computed(
    () =>
      isLoadingSummary.value ||
      isLoadingUserAttempts.value ||
      isLoadingAnswerSummary.value ||
      isLoadingAllResponses.value ||
      loading.value,
  );
  const startAttempt = async (userId: number, quizId: number) => {
    loading.value = true;
    try {
      const { data } = await quizAttemptService.startQuizAttempt(userId, quizId);
      currentAttempt.value = data;
      const navigateToQuestionData = await quizAttemptService.navigateToQuestion(data.id, 1);
      currentQuestion.value = navigateToQuestionData.data.question;
      currentAttemptAnswer.value = navigateToQuestionData.data.attemptAnswer;
    } finally {
      loading.value = false;
    }
  };

  const loadActiveAttempt = async () => {
    loading.value = true;
    try {
      const { data } = await quizAttemptService.getActiveQuizAttempt();
      currentAttempt.value = data;
    } finally {
      loading.value = false;
    }
  };

  const loadQuestion = async (sequence: number) => {
    if (!currentAttempt.value) return;
    loading.value = true;
    try {
      const { data } = await quizAttemptService.navigateToQuestion(
        currentAttempt.value.id,
        sequence,
      );
      currentQuestion.value = data.question;
      currentAttemptAnswer.value = data.attemptAnswer;
    } finally {
      loading.value = false;
    }
  };

  const saveAnswer = async (answer: unknown) => {
    if (!currentAttempt.value) return;
    await quizAttemptService.saveAnswer(currentAttempt.value.id, answer);
    currentAttemptAnswer.value = answer;
  };

  const submitAttempt = async () => {
    if (!currentAttempt.value) return;
    const { data } = await quizAttemptService.submitQuizAttempt(currentAttempt.value.id);
    submitResult.value = data;
  };

  // --- Dashboard Actions ---
  function setQuizId(id: number | null) {
    if (currentQuizId.value !== id) {
      currentQuizId.value = id;
      quizSummary.value = null;
      userAttempts.value = null;
      answerSummary.value = null;
      allResponsesData.value = null; // <--- RESET allResponsesData
      errorSummary.value = null;
      errorUserAttempts.value = null;
      errorAnswerSummary.value = null;
      errorAllResponses.value = null; // <--- RESET errorAllResponses
    }
  }

  async function fetchQuizSummary(quizIdToFetch?: number) {
    const id = quizIdToFetch || currentQuizId.value;
    if (id === null) {
      errorSummary.value = 'Quiz ID is not set for summary.';
      return;
    }
    isLoadingSummary.value = true;
    errorSummary.value = null;
    try {
      // ---- เปลี่ยนไปใช้ AssessmentService.getMetaResponse ----
      const metaData: QuizMetaResponse = await AssessmentService.getMetaResponse(id);
      quizSummary.value = {
        quizId: id,
        quizTitle: metaData.assessmentName,
        numberOfAttempts: metaData.uniqueUsers,
        highestScore: metaData.highestScore,
        lowestScore: metaData.lowestScore,
      };
    } catch (err: any) {
      errorSummary.value =
        err?.response?.data?.message || err?.message || 'Failed to load quiz summary (meta).';
      $q.notify({ type: 'negative', message: errorSummary.value });
      quizSummary.value = null;
    } finally {
      isLoadingSummary.value = false;
    }
  }

  async function fetchUserAttempts(params: unknown = {}, quizIdToFetch?: number) {
    const id = quizIdToFetch || currentQuizId.value;
    if (id === null) {
      errorUserAttempts.value = 'Quiz ID is not set for fetching user attempts.';
      $q.notify({ type: 'warning', message: errorUserAttempts.value });
      return;
    }
    isLoadingUserAttempts.value = true;
    errorUserAttempts.value = null;
    try {
      userAttempts.value = await quizDashboardService.getUserAttempts(id, params);
    } catch (err) {
      errorUserAttempts.value =
        err?.response?.data?.message || err?.message || 'Failed to load user attempts.';
      $q.notify({
        type: 'negative',
        message: errorUserAttempts.value || 'Error loading user attempts',
      });
      userAttempts.value = null;
    } finally {
      isLoadingUserAttempts.value = false;
    }
  }

  async function fetchAnswerSummary(quizIdToFetch?: number) {
    const id = quizIdToFetch || currentQuizId.value;
    if (id === null) {
      errorAnswerSummary.value = 'Quiz ID is not set for fetching answer summary.';
      $q.notify({ type: 'warning', message: errorAnswerSummary.value });
      return;
    }
    isLoadingAnswerSummary.value = true;
    errorAnswerSummary.value = null;
    try {
      answerSummary.value = await quizDashboardService.getQuizAnswerSummary(id);
    } catch (err) {
      errorAnswerSummary.value =
        err?.response?.data?.message || err?.message || 'Failed to load answer summary.';
      $q.notify({
        type: 'negative',
        message: errorAnswerSummary.value || 'Error loading answer summary',
      });
      answerSummary.value = null;
    } finally {
      isLoadingAnswerSummary.value = false;
    }
  }

  async function fetchAllQuizDataForCurrentId() {
    if (currentQuizId.value === null) {
      console.warn('Cannot fetch all quiz data, Quiz ID is not set.');
      isLoadingSummary.value = false;
      isLoadingAllResponses.value = false;
      isLoadingUserAttempts.value = false; // ถ้ายังใช้
      isLoadingAnswerSummary.value = false; // ถ้ายังใช้
      return;
    }

    // ตั้งค่า individual loading flags เป็น true ก่อนเรียก Promise.allSettled
    // overallLoading จะอัปเดตตาม flags เหล่านี้
    isLoadingSummary.value = true;
    isLoadingAllResponses.value = true;
    // ถ้า fetchUserAttempts และ fetchAnswerSummary ยังเป็นส่วนหนึ่งของการโหลดทั้งหมด ก็ตั้งค่า loading ด้วย:
    // isLoadingUserAttempts.value = true;
    // isLoadingAnswerSummary.value = true;

    await Promise.allSettled([
      fetchQuizSummary(currentQuizId.value), // เรียก fetchQuizSummary ที่อัปเดตแล้ว
      fetchAllQuizResponses(currentQuizId.value), // <--- เพิ่มการเรียก ACTION ใหม่
      // ถ้ายังใช้ fetchUserAttempts และ fetchAnswerSummary และต้องการให้โหลดพร้อมกัน:
      // fetchUserAttempts({}, currentQuizId.value),
      // fetchAnswerSummary(currentQuizId.value),
    ]);
  }
  async function fetchAllQuizResponses(quizIdToFetch?: number) {
    const id = quizIdToFetch || currentQuizId.value;
    if (id === null) {
      errorAllResponses.value = 'Quiz ID is not set for fetching all responses.';
      return;
    }
    isLoadingAllResponses.value = true;
    errorAllResponses.value = null;
    try {
      // ---- ใช้ AssessmentService.getAllResponses ----
      allResponsesData.value = await AssessmentService.getAllResponses(id);
    } catch (err: any) {
      errorAllResponses.value =
        err?.response?.data?.message || err?.message || 'Failed to load all quiz responses.';
      $q.notify({ type: 'negative', message: errorAllResponses.value });
      allResponsesData.value = null;
    } finally {
      isLoadingAllResponses.value = false;
    }
  }

  async function fetchQuizById(id: number) {
    loading.value = true;
    try {
      console.log('fetchQuizById (request): ', id);
      const res = await new AssessmentService('quiz').fetchOne(id);
      currentAssessment.value = res;
      console.log('fetchQuizById (response): ', currentAssessment.value);
    } catch (err: any) {
      $q.notify({ type: 'negative', message: err?.message ?? 'โหลด Quiz ไม่สำเร็จ' });
    } finally {
      loading.value = false;
    }
  }

  async function addQuiz(quizData: Partial<Assessment>) {
    try {
      const res = await new AssessmentService('quiz').createOne(quizData);
      quizzes.value.push(res);
      currentAssessment.value = res;
      console.log();
      $q.notify({ type: 'positive', message: 'สร้าง Quiz สำเร็จ' });
    } catch (err: any) {
      $q.notify({ type: 'negative', message: err?.message ?? 'สร้าง Quiz ไม่สำเร็จ' });
    }
  }

  async function updateQuiz(id: number, updatedData: Assessment) {
    try {
      const res = await new AssessmentService('quiz').updateOne(id, updatedData);
      const index = quizzes.value.findIndex((q) => q.id === id);
      if (index !== -1) quizzes.value[index] = res;
      if (currentAssessment.value?.id === id) currentAssessment.value = res;
      $q.notify({ type: 'positive', message: 'บันทึกการแก้ไข Quiz แล้ว' });
    } catch (err: any) {
      $q.notify({ type: 'negative', message: err?.message ?? 'อัปเดต Quiz ไม่สำเร็จ' });
    }
  }

  async function removeQuiz(id: number) {
    try {
      await new AssessmentService('quiz').deleteOne(id);
      quizzes.value = quizzes.value.filter((q) => q.id !== id);
      if (currentAssessment.value?.id === id) currentAssessment.value = null;
      $q.notify({ type: 'positive', message: 'ลบ Quiz สำเร็จ' });
    } catch (err: any) {
      $q.notify({ type: 'negative', message: err?.message ?? 'ลบ Quiz ไม่สำเร็จ' });
    }
  }

  // --- Reset ---
  const resetStore = () => {
    currentAttempt.value = null;
    currentQuestion.value = null;
    currentAttemptAnswer.value = null;
    submitResult.value = null;
    loading.value = false;
    currentQuizId.value = null;
    currentAssessment.value = null;
    quizSummary.value = null;
    userAttempts.value = null;
    answerSummary.value = null;
    allResponsesData.value = null;

    isLoadingUserAttempts.value = false;
    isLoadingAnswerSummary.value = false;
    isLoadingSummary.value = false;
    isLoadingAllResponses.value = false;

    errorSummary.value = null;
    errorUserAttempts.value = null;
    errorAnswerSummary.value = null;
    errorAllResponses.value = null;
  };

  return {
    // Attempt State
    currentAttempt,
    currentQuestion,
    currentAttemptAnswer,
    submitResult,
    loading,
    startAttempt,
    loadActiveAttempt,
    loadQuestion,
    saveAnswer,
    submitAttempt,
    // Dashboard State
    currentQuizId,
    quizSummary,
    userAttempts,
    answerSummary,
    isLoadingSummary,
    isLoadingUserAttempts,
    isLoadingAnswerSummary,
    errorSummary,
    errorUserAttempts,
    errorAnswerSummary,
    hasQuizData,
    overallLoading,
    allResponsesData,
    isLoadingAllResponses,
    errorAllResponses,
    setQuizId,
    fetchQuizSummary,
    fetchUserAttempts,
    fetchAnswerSummary,
    fetchAllQuizDataForCurrentId,
    fetchAllQuizResponses,
    // CRUD Quiz State
    quizzes,
    currentAssessment,
    fetchQuizById,
    addQuiz,
    updateQuiz,
    removeQuiz,
    // Reset
    resetStore,
  };
});
