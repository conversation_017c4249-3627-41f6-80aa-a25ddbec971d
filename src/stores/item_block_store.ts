import { defineStore } from 'pinia';
import type { ItemBlock, Option, Question, ItemBlockType } from 'src/types/models';
import { ref } from 'vue';

// Define the store type for TypeScript
export interface ItemBlockStore {
  // State
  radioOptions: {
    id?: number;
    placeholder: string;
    value: string;
    optionText: string;
    score: number;
    sequence: number;
  }[];
  optionSelectedOption: string[];
  checkboxOptions: {
    id?: number;
    placeholder: string;
    value: string;
    optionText: string;
    score: number;
    sequence: number;
  }[];
  checkboxSelectedOptions: string[];
  gridRowQuestions: { id?: number; label: string; value: string; sequence: number }[];
  // Shared column options for all grid row questions
  gridColumnOptions: {
    id?: number;
    label: string;
    value: string;
    optionText: string;
    score: number;
    sequence: number;
  }[];
  textInput: string;
  draggedIndex: number | null;
  draggedSection: 'row' | 'col' | null;
  hoveredIndex: number | null;

  // Actions
  updateOption: (index: number, isCheckbox?: boolean) => void;
  addOption: (isCheckbox?: boolean) => void;
  addOtherOption: (isCheckbox?: boolean) => void;
  removeOption: (index: number, isCheckbox?: boolean) => void;
  addRowQuestion: () => void;
  addColumnOption: () => void;
  removeRowQuestion: (index: number) => void;
  removeColumnOption: (index: number) => void;
  updateRowQuestion: (index: number) => void;
  updateColumnOption: (index: number) => void;
  startDrag: (index: number, section?: 'row' | 'col') => void;
  handleDragStart: (event: DragEvent) => void;
  hoverRow: (index: number) => void;
  drop: (index: number, event: DragEvent, isCheckbox?: boolean) => void;
  gridDrop: (index: number, event: DragEvent, section: 'row' | 'col') => void;
  endDrag: () => void;

  // Extract methods
  extractOptions: () => Option[];
  extractQuestions: () => Question[];
  extractItemBlockData: () => {
    options: Option[];
    questions: Question[];
    type?: ItemBlockType;
    sequence?: number;
    section?: number;
    isRequired: boolean;
  };

  // Helper methods for ID management
  findOptionById: (
    optionId: number,
    isCheckbox?: boolean,
  ) => {
    option: {
      id?: number;
      placeholder: string;
      value: string;
      optionText: string;
      score: number;
      sequence: number;
    } | null;
    index: number;
  };
  updateOptionId: (index: number, optionId: number, isCheckbox?: boolean) => void;
  syncOptionSequences: (isCheckbox?: boolean) => Promise<void>;
  syncGridColumnSequences: () => Promise<void>;

  // Options synchronization for type changes
  syncOptionsFromRadioToCheckbox: () => void;
  syncOptionsFromCheckboxToRadio: () => void;

  // Re-initialization method for updated itemBlock data
  reinitializeWithUpdatedData: (updatedItemBlock: ItemBlock) => void;
}

export const createItemBlockStore = (id: number, itemBlock?: ItemBlock) =>
  defineStore(`item-block-${id}`, () => {
    // Define a type for options
    type OptionType = {
      id?: number; // Add optional ID for backend sync
      placeholder: string;
      value: string;
      optionText: string;
      score: number; // Add score property
      sequence: number; // Add sequence property
    };

    // Define a type for grid row questions
    type GridRowQuestion = {
      id?: number; // Add optional ID for backend sync
      label: string;
      value: string;
      sequence: number; // Add sequence property
    };

    // Define a type for grid column options
    type GridColumnOption = {
      id?: number; // Add optional ID for backend sync
      label: string;
      value: string;
      optionText: string;
      score: number; // Add score property
      sequence: number; // Add sequence property
    };

    //state
    //option
    const radioOptions = ref<OptionType[]>([
      { placeholder: '', value: '', optionText: '', score: 0, sequence: 1 },
    ]);

    // Add "other" option conditionally for radio type
    // if (itemBlock?.type === 'RADIO') {
    //   radioOptions.value.push({
    //     placeholder: 'อื่นๆ',
    //     value: '',
    //     optionText: '',
    //     score: 0,
    //     sequence: 2,
    //   });
    // }

    const optionSelectedOption = ref<string[]>([]);

    //checkbox
    const checkboxOptions = ref<OptionType[]>([
      { placeholder: '', value: '', optionText: '', score: 0, sequence: 1 },
    ]);
    const checkboxSelectedOptions = ref<string[]>([]);

    //grid
    const gridRowQuestions = ref<GridRowQuestion[]>([
      { label: '', value: 'question1', sequence: 1 },
    ]);
    const gridColumnOptions = ref<GridColumnOption[]>([
      { label: '', value: 'option1', optionText: '', score: 0, sequence: 1 },
    ]);

    //text
    const textInput = ref('');
    const draggedIndex = ref<number | null>(null);
    const draggedSection = ref<'row' | 'col' | null>(null);
    const hoveredIndex = ref<number | null>(null);

    // Initialize store with itemBlock data if available
    if (itemBlock) {
      // Initialize radio/checkbox options
      if (itemBlock.options && itemBlock.options.length > 0) {
        // Sort options by sequence before mapping to ensure correct order
        const sortedOptions = [...itemBlock.options].sort((a, b) => a.sequence - b.sequence);

        console.log('🔄 [STORE] Initializing options with correct sequence order:', {
          itemBlockId: id,
          itemBlockType: itemBlock.type,
          originalOrder: itemBlock.options.map((opt) => ({
            id: opt.id,
            sequence: opt.sequence,
            text: opt.optionText,
          })),
          sortedOrder: sortedOptions.map((opt) => ({
            id: opt.id,
            sequence: opt.sequence,
            text: opt.optionText,
          })),
        });

        const mappedOptions = sortedOptions.map((opt) => ({
          id: opt.id, // Include backend ID for sync
          placeholder: opt.optionText || `ตัวเลือกที่ ${opt.sequence}`,
          value: String(opt.value || `option${opt.sequence}`),
          optionText: opt.optionText || '',
          score: opt.value || 0, // Use actual value from backend
          sequence: opt.sequence,
        }));

        if (itemBlock.type === 'RADIO') {
          radioOptions.value = mappedOptions;
        } else if (itemBlock.type === 'CHECKBOX') {
          checkboxOptions.value = mappedOptions;
        } else if (itemBlock.type === 'GRID') {
          // Sort grid column options by sequence as well
          const sortedGridOptions = [...itemBlock.options].sort((a, b) => a.sequence - b.sequence);

          console.log('🔄 [STORE] Initializing GRID column options with correct sequence order:', {
            itemBlockId: id,
            originalOrder: itemBlock.options.map((opt) => ({
              id: opt.id,
              sequence: opt.sequence,
              text: opt.optionText,
            })),
            sortedOrder: sortedGridOptions.map((opt) => ({
              id: opt.id,
              sequence: opt.sequence,
              text: opt.optionText,
            })),
          });

          gridColumnOptions.value = sortedGridOptions.map((option, idx) => ({
            id: option.id, // Include backend ID for sync
            label: option.optionText || `ตัวเลือกที่ ${option.sequence}`,
            value: `option${idx + 1}`,
            optionText: option.optionText || '',
            score: option.value || 0,
            sequence: option.sequence,
          }));
        }
      }

      // Initialize grid row questions if it's a grid type
      if (itemBlock.type === 'GRID' && itemBlock.questions && itemBlock.questions.length > 0) {
        // Skip the header question (isHeader === true) and sort by sequence
        const rowQuestions = itemBlock.questions
          .filter((q) => !q.isHeader)
          .sort((a, b) => a.sequence - b.sequence);

        if (rowQuestions.length > 0) {
          console.log('🔄 [STORE] Initializing GRID row questions with correct sequence order:', {
            itemBlockId: id,
            questionsOrder: rowQuestions.map((q) => ({
              id: q.id,
              sequence: q.sequence,
              text: q.questionText,
            })),
          });

          gridRowQuestions.value = rowQuestions.map((q, idx) => ({
            id: q.id, // Include backend ID for sync
            label: q.questionText || '',
            value: `question${idx + 1}`,
            sequence: q.sequence,
          }));
        }
      }

      // Initialize text input if it's a text field
      if (itemBlock.type === 'TEXTFIELD' && itemBlock.questions && itemBlock.questions.length > 0) {
        textInput.value = itemBlock.questions[0]?.questionText || '';
      }
    }

    // Actions
    const addOption = (isCheckbox: boolean = false) => {
      const options = isCheckbox ? checkboxOptions.value : radioOptions.value;
      const newIndex = options.length + 1;
      options.push({
        placeholder: '',
        value: ``,
        optionText: ``, // Empty by default, only show placeholder
        score: 0,
        sequence: newIndex, // Add sequence property
      });
    };

    const addOtherOption = (isCheckbox: boolean = false) => {
      const options = isCheckbox ? checkboxOptions.value : radioOptions.value;
      const newIndex = options.length + 1;
      options.push({
        placeholder: 'อื่นๆ',
        value: ``,
        optionText: '', // Empty by default, only show placeholder
        score: 0,
        sequence: newIndex, // Add sequence property
      });
    };

    const updateOption = (index: number, isCheckbox: boolean = false) => {
      const options = isCheckbox ? checkboxOptions.value : radioOptions.value;
      options[index]!.value =
        options[index]!.placeholder === 'อื่นๆ' ? `other${index + 1}` : `option${index + 1}`;

      // Don't automatically set optionText to placeholder
      // Let the user enter their own text

      // Ensure score is a number (don't reset to 0)
      const score = options[index]!.score;
      if (typeof score !== 'number') {
        options[index]!.score = Number(score) || 0;
      }
    };

    const removeOption = (index: number, isCheckbox: boolean = false) => {
      const options = isCheckbox ? checkboxOptions.value : radioOptions.value;
      const selectedOptionsRef = isCheckbox ? checkboxSelectedOptions : optionSelectedOption;

      if (options.length > 1) {
        console.log('🗑️ [STORE] Removing option at index:', {
          index,
          isCheckbox,
          optionId: options[index]?.id,
          optionText: options[index]?.optionText,
          totalOptions: options.length,
        });

        // Simply remove the option at the specified index
        // This preserves all other options' IDs and properties
        options.splice(index, 1);

        // Update sequence numbers for remaining options to maintain order
        options.forEach((option, idx) => {
          option.sequence = idx + 1;
          // Update value to maintain consistency (but preserve ID and other properties)
          option.value = option.placeholder === 'อื่นๆ' ? `other${idx + 1}` : `option${idx + 1}`;
        });

        console.log('✅ [STORE] Option removed, remaining options:', {
          isCheckbox,
          remainingCount: options.length,
          remainingOptions: options.map((opt, idx) => ({
            index: idx,
            id: opt.id,
            text: opt.optionText,
            sequence: opt.sequence,
          })),
        });

        // Update selected options to remove any references to deleted options
        selectedOptionsRef.value = selectedOptionsRef.value.filter((val) =>
          options.some((option) => option.value === val),
        );
      }
    };

    //grid
    const addRowQuestion = () => {
      const newIndex = gridRowQuestions.value.length + 1;

      gridRowQuestions.value.push({
        label: ``, // Empty by default, only show placeholder
        value: `question${newIndex}`,
        sequence: newIndex, // Add sequence property
      });
    };

    const addColumnOption = () => {
      const newIndex = gridColumnOptions.value.length + 1;
      gridColumnOptions.value.push({
        label: '',
        value: `option${newIndex}`,
        optionText: ``, // Empty by default, only show placeholder
        score: 0, // Default score value
        sequence: newIndex, // Add sequence property
      });
    };

    const removeRowQuestion = (index: number) => {
      if (gridRowQuestions.value.length > 1) {
        // Remove the question
        gridRowQuestions.value.splice(index, 1);

        // Renumber questions but preserve user-entered labels
        gridRowQuestions.value = gridRowQuestions.value.map((question, idx) => ({
          ...question,
          value: `question${idx + 1}`,
          // Preserve the original label entered by the user
          sequence: idx + 1,
        }));
      }
    };

    const removeColumnOption = (index: number) => {
      if (gridColumnOptions.value.length > 1) {
        gridColumnOptions.value.splice(index, 1);
        gridColumnOptions.value = gridColumnOptions.value.map((option, idx) => ({
          ...option,
          value: `option${idx + 1}`,
          // Preserve user-entered optionText
          // Preserve user-entered score
          sequence: idx + 1,
        }));
      }
    };

    const updateRowQuestion = (index: number) => {
      gridRowQuestions.value[index]!.value = `question${index + 1}`;

      // Don't automatically set label text
      // Let the user enter their own text
    };

    const updateColumnOption = (index: number) => {
      gridColumnOptions.value[index]!.value = `option${index + 1}`;

      // Don't automatically set optionText to label
      // Let the user enter their own text

      // Ensure score is a number (don't reset to 0)
      const score = gridColumnOptions.value[index]!.score;
      if (typeof score !== 'number') {
        gridColumnOptions.value[index]!.score = Number(score) || 0;
      }
    };

    //drag func
    const startDrag = (index: number, section?: 'row' | 'col') => {
      draggedIndex.value = index;
      if (section) draggedSection.value = section;
    };

    const handleDragStart = (event: DragEvent) => {
      if (event.dataTransfer) {
        event.dataTransfer.effectAllowed = 'move';
      }
    };

    const hoverRow = (index: number) => {
      hoveredIndex.value = index;
    };

    const drop = (index: number, event: DragEvent, isCheckbox: boolean = false) => {
      event.preventDefault();
      if (draggedIndex.value === null || draggedIndex.value === index) return;

      const options = isCheckbox ? checkboxOptions.value : radioOptions.value;
      const originalDraggedIndex = draggedIndex.value;

      console.log('🎯 [STORE] Starting drag-and-drop operation:', {
        isCheckbox,
        fromIndex: originalDraggedIndex,
        toIndex: index,
        totalOptions: options.length,
      });

      // Create a copy of the dragged option with all properties including ID
      const draggedOption: OptionType = {
        ...(options[originalDraggedIndex]!.id && { id: options[originalDraggedIndex]!.id }), // Preserve the ID if it exists
        placeholder: options[originalDraggedIndex]!.placeholder,
        value: options[originalDraggedIndex]!.value,
        optionText: options[originalDraggedIndex]!.optionText,
        score: options[originalDraggedIndex]!.score,
        sequence: options[originalDraggedIndex]!.sequence,
      };

      // Perform the reordering
      options.splice(originalDraggedIndex, 1);
      options.splice(index, 0, draggedOption);

      // Update all options with new values and sequence numbers
      options.forEach((option, idx) => {
        // Update the value based on the placeholder
        option.value = option.placeholder === 'อื่นๆ' ? `other${idx + 1}` : `option${idx + 1}`;

        // Ensure score is preserved as a number
        if (typeof option.score !== 'number') {
          option.score = Number(option.score) || 0;
        }

        // Update the sequence value to match the new position
        // Add 1 to make it 1-based instead of 0-based
        option.sequence = idx + 1;
      });

      console.log('✅ [STORE] Local reordering completed:', {
        isCheckbox,
        newOrder: options.map((opt, idx) => ({
          index: idx,
          id: opt.id,
          sequence: opt.sequence,
          text: opt.optionText,
        })),
      });

      // Reset drag state
      draggedIndex.value = null;
      hoveredIndex.value = null;

      // Sync with backend (don't await to avoid blocking UI)
      syncOptionSequences(isCheckbox).catch((error) => {
        console.error('❌ [STORE] Failed to sync sequences after drag-and-drop:', error);
      });
    };

    const gridDrop = (index: number, event: DragEvent, section: 'row' | 'col') => {
      event.preventDefault();
      if (
        draggedIndex.value === null ||
        draggedSection.value !== section ||
        draggedIndex.value === index
      )
        return;

      const originalDraggedIndex = draggedIndex.value;

      console.log('🎯 [STORE] Starting grid drag-and-drop operation:', {
        section,
        fromIndex: originalDraggedIndex,
        toIndex: index,
      });

      if (section === 'row') {
        const sourceList = gridRowQuestions.value;
        const draggedItem = {
          label: sourceList[originalDraggedIndex]!.label,
          value: sourceList[originalDraggedIndex]!.value,
          sequence: sourceList[originalDraggedIndex]!.sequence,
          // Preserve any other properties
          ...sourceList[originalDraggedIndex],
        };
        sourceList.splice(originalDraggedIndex, 1);
        gridRowQuestions.value.splice(index, 0, draggedItem);
      } else {
        const sourceList = gridColumnOptions.value;
        const draggedItem = {
          ...(sourceList[originalDraggedIndex]!.id && { id: sourceList[originalDraggedIndex]!.id }), // Preserve ID if it exists
          label: sourceList[originalDraggedIndex]!.label,
          value: sourceList[originalDraggedIndex]!.value,
          optionText: sourceList[originalDraggedIndex]!.optionText,
          score: sourceList[originalDraggedIndex]!.score,
          sequence: sourceList[originalDraggedIndex]!.sequence,
          // Preserve any other properties
          ...sourceList[originalDraggedIndex],
        };
        sourceList.splice(originalDraggedIndex, 1);
        gridColumnOptions.value.splice(index, 0, draggedItem);
      }

      if (section === 'row') {
        gridRowQuestions.value = gridRowQuestions.value.map((question, idx) => ({
          ...question,
          value: `question${idx + 1}`,
          // Preserve user-entered label
          // Update sequence value to match new position
          sequence: idx + 1,
        }));
      } else {
        gridColumnOptions.value = gridColumnOptions.value.map((option, idx) => ({
          ...option,
          value: `option${idx + 1}`,
          // Preserve user-entered optionText
          // Preserve the score value
          score: typeof option.score === 'number' ? option.score : Number(option.score) || 0,
          // Update sequence value to match new position
          sequence: idx + 1,
        }));

        console.log('✅ [STORE] Grid column reordering completed:', {
          newOrder: gridColumnOptions.value.map((opt, idx) => ({
            index: idx,
            id: opt.id,
            sequence: opt.sequence,
            text: opt.optionText,
          })),
        });
      }

      draggedIndex.value = null;
      draggedSection.value = null;
      hoveredIndex.value = null;

      // Sync with backend for column options only (don't await to avoid blocking UI)
      if (section === 'col') {
        syncGridColumnSequences().catch((error) => {
          console.error(
            '❌ [STORE] Failed to sync grid column sequences after drag-and-drop:',
            error,
          );
        });
      }
    };

    const endDrag = () => {
      draggedIndex.value = null;
      draggedSection.value = null;
      hoveredIndex.value = null;
    };

    // Extract options and questions for API
    const extractOptions = (): Option[] => {
      if (itemBlock?.type === 'RADIO') {
        return radioOptions.value.map((option, index) => ({
          id: 0, // Will be set by backend
          itemBlockId: id,
          optionText: option.optionText || option.placeholder,
          value: option.score || 0,
          sequence: option.sequence || index + 1,
        }));
      } else if (itemBlock?.type === 'CHECKBOX') {
        return checkboxOptions.value.map((option, index) => ({
          id: 0, // Will be set by backend
          itemBlockId: id,
          optionText: option.optionText || option.placeholder,
          value: option.score || 0,
          sequence: option.sequence || index + 1,
        }));
      } else if (itemBlock?.type === 'GRID') {
        return gridColumnOptions.value.map((option, index) => ({
          id: option.id || 0, // Use existing ID if available, otherwise backend will set
          itemBlockId: id,
          optionText: option.optionText || option.label,
          value: option.score || 0,
          sequence: option.sequence || index + 1,
        }));
      }
      return [];
    };

    const extractQuestions = (): Question[] => {
      const questions: Question[] = [];

      if (itemBlock?.type === 'TEXTFIELD') {
        questions.push({
          id: 0, // Will be set by backend
          itemBlockId: id,
          questionText: textInput.value || '',
          isHeader: false,
          sequence: 1,
          score: 0,
        });
      } else if (itemBlock?.type === 'GRID') {
        // Add header question first if it exists
        const headerQuestion = itemBlock?.questions?.find((q) => q.isHeader);
        if (headerQuestion) {
          questions.push({
            ...headerQuestion,
            itemBlockId: id,
          });
        }

        // Add row questions with isHeader: false (REQUIRED for GRID type)
        gridRowQuestions.value.forEach((rowQuestion, index) => {
          questions.push({
            id: rowQuestion.id || 0, // Use existing ID if available, otherwise backend will set
            itemBlockId: id,
            questionText: rowQuestion.label || `Question ${index + 1}`,
            isHeader: false, // REQUIRED: All GRID questions must have isHeader: false
            sequence: rowQuestion.sequence || index + (headerQuestion ? 2 : 1),
            score: 0,
          });
        });
      } else if (itemBlock?.type === 'UPLOAD') {
        // For file type, extract any existing questions
        const existingQuestions = itemBlock?.questions || [];
        existingQuestions.forEach((question) => {
          questions.push({
            ...question,
            itemBlockId: id,
          });
        });
      }

      return questions;
    };

    const extractItemBlockData = () => {
      return {
        options: extractOptions(),
        questions: extractQuestions(),
        type: itemBlock?.type,
        sequence: itemBlock?.sequence,
        section: itemBlock?.section,
        isRequired: itemBlock?.isRequired || false,
      };
    };

    // Helper function to find option by ID in the store
    const findOptionById = (
      optionId: number,
      isCheckbox: boolean = false,
    ): { option: OptionType | null; index: number } => {
      const options = isCheckbox ? checkboxOptions.value : radioOptions.value;
      const index = options.findIndex((option) => option.id === optionId);
      return {
        option: index >= 0 ? options[index] || null : null,
        index: index >= 0 ? index : -1,
      };
    };

    // Helper function to update option ID in the store after creation
    const updateOptionId = (index: number, optionId: number, isCheckbox: boolean = false) => {
      const options = isCheckbox ? checkboxOptions.value : radioOptions.value;
      if (options[index]) {
        options[index].id = optionId;
      }
    };

    // Helper function to sync option sequences with backend after drag-and-drop
    const syncOptionSequences = async (isCheckbox: boolean = false) => {
      try {
        const options = isCheckbox ? checkboxOptions.value : radioOptions.value;

        // Only sync options that have IDs (created in backend)
        const optionsWithIds = options.filter((option) => option.id);

        if (optionsWithIds.length === 0) {
          console.log('🔄 [STORE] No options with IDs to sync');
          return;
        }

        console.log('🔄 [STORE] Syncing option sequences with backend:', {
          isCheckbox,
          optionsCount: optionsWithIds.length,
          itemBlockId: id,
        });

        // Import OptionService dynamically to avoid circular dependencies
        const { OptionService } = await import('src/services/asm/optionService');
        const optionService = new OptionService();

        // Prepare options data for API call
        const optionsData = optionsWithIds.map((option) => ({
          id: option.id!,
          sequence: option.sequence,
          optionText: option.optionText,
          value: option.score,
        }));

        // Call API to update sequences
        await optionService.updateOptionSequences(optionsData, id);

        console.log('✅ [STORE] Successfully synced option sequences with backend');
      } catch (error) {
        console.error('❌ [STORE] Failed to sync option sequences:', error);
        // Don't throw error to avoid breaking the UI - the local state is still updated
      }
    };

    // Helper function to sync grid column option sequences with backend after drag-and-drop
    const syncGridColumnSequences = async () => {
      try {
        const options = gridColumnOptions.value;

        // Only sync options that have IDs (created in backend)
        const optionsWithIds = options.filter((option) => option.id);

        if (optionsWithIds.length === 0) {
          console.log('🔄 [STORE] No grid column options with IDs to sync');
          return;
        }

        console.log('🔄 [STORE] Syncing grid column option sequences with backend:', {
          optionsCount: optionsWithIds.length,
          itemBlockId: id,
        });

        // Import OptionService dynamically to avoid circular dependencies
        const { OptionService } = await import('src/services/asm/optionService');
        const optionService = new OptionService();

        // Prepare options data for API call
        const optionsData = optionsWithIds.map((option) => ({
          id: option.id!,
          sequence: option.sequence,
          optionText: option.optionText,
          value: option.score,
        }));

        // Call API to update sequences
        await optionService.updateOptionSequences(optionsData, id);

        console.log('✅ [STORE] Successfully synced grid column option sequences with backend');
      } catch (error) {
        console.error('❌ [STORE] Failed to sync grid column option sequences:', error);
        // Don't throw error to avoid breaking the UI - the local state is still updated
      }
    };

    // Options synchronization methods for type changes
    const syncOptionsFromRadioToCheckbox = () => {
      console.log('🔄 [STORE] Syncing options from RADIO to CHECKBOX:', {
        radioOptionsCount: radioOptions.value.length,
        radioOptions: radioOptions.value.map((opt, idx) => ({
          index: idx,
          id: opt.id,
          text: opt.optionText,
          score: opt.score,
        })),
      });

      // Copy all radio options to checkbox options
      checkboxOptions.value = radioOptions.value.map((radioOption) => ({
        id: radioOption.id,
        placeholder: radioOption.placeholder,
        value: radioOption.value,
        optionText: radioOption.optionText,
        score: radioOption.score,
        sequence: radioOption.sequence,
      }));

      console.log('✅ [STORE] Successfully synced options to CHECKBOX:', {
        checkboxOptionsCount: checkboxOptions.value.length,
        checkboxOptions: checkboxOptions.value.map((opt, idx) => ({
          index: idx,
          id: opt.id,
          text: opt.optionText,
          score: opt.score,
        })),
      });
    };

    const syncOptionsFromCheckboxToRadio = () => {
      console.log('🔄 [STORE] Syncing options from CHECKBOX to RADIO:', {
        checkboxOptionsCount: checkboxOptions.value.length,
        checkboxOptions: checkboxOptions.value.map((opt, idx) => ({
          index: idx,
          id: opt.id,
          text: opt.optionText,
          score: opt.score,
        })),
      });

      // Copy all checkbox options to radio options
      radioOptions.value = checkboxOptions.value.map((checkboxOption) => ({
        id: checkboxOption.id,
        placeholder: checkboxOption.placeholder,
        value: checkboxOption.value,
        optionText: checkboxOption.optionText,
        score: checkboxOption.score,
        sequence: checkboxOption.sequence,
      }));

      console.log('✅ [STORE] Successfully synced options to RADIO:', {
        radioOptionsCount: radioOptions.value.length,
        radioOptions: radioOptions.value.map((opt, idx) => ({
          index: idx,
          id: opt.id,
          text: opt.optionText,
          score: opt.score,
        })),
      });
    };

    // Re-initialization method for updated itemBlock data
    const reinitializeWithUpdatedData = (updatedItemBlock: ItemBlock) => {
      console.log('🔄 [STORE] Re-initializing store with updated ItemBlock data:', {
        itemBlockId: updatedItemBlock.id,
        type: updatedItemBlock.type,
        optionsCount: updatedItemBlock.options?.length || 0,
      });

      // Clear existing data first
      radioOptions.value = [{ placeholder: '', value: '', optionText: '', score: 0, sequence: 1 }];
      checkboxOptions.value = [
        { placeholder: '', value: '', optionText: '', score: 0, sequence: 1 },
      ];
      gridRowQuestions.value = [{ label: '', value: 'question1', sequence: 1 }];
      gridColumnOptions.value = [
        { label: '', value: 'option1', optionText: '', score: 0, sequence: 1 },
      ];
      textInput.value = '';

      // Re-initialize with updated data using the same logic as initial setup
      if (updatedItemBlock.options && updatedItemBlock.options.length > 0) {
        const sortedOptions = [...updatedItemBlock.options].sort((a, b) => a.sequence - b.sequence);

        const mappedOptions = sortedOptions.map((opt) => ({
          id: opt.id,
          placeholder: opt.optionText || `ตัวเลือกที่ ${opt.sequence}`,
          value: String(opt.value || `option${opt.sequence}`),
          optionText: opt.optionText || '',
          score: opt.value || 0,
          sequence: opt.sequence,
        }));

        if (updatedItemBlock.type === 'RADIO') {
          radioOptions.value = mappedOptions;
          console.log('✅ [STORE] Re-initialized RADIO options:', {
            count: radioOptions.value.length,
            options: radioOptions.value.map((opt, idx) => ({
              index: idx,
              id: opt.id,
              text: opt.optionText,
            })),
          });
        } else if (updatedItemBlock.type === 'CHECKBOX') {
          checkboxOptions.value = mappedOptions;
          console.log('✅ [STORE] Re-initialized CHECKBOX options:', {
            count: checkboxOptions.value.length,
            options: checkboxOptions.value.map((opt, idx) => ({
              index: idx,
              id: opt.id,
              text: opt.optionText,
            })),
          });
        } else if (updatedItemBlock.type === 'GRID') {
          gridColumnOptions.value = sortedOptions.map((option, idx) => ({
            id: option.id,
            label: option.optionText || `ตัวเลือกที่ ${option.sequence}`,
            value: `option${idx + 1}`,
            optionText: option.optionText || '',
            score: option.value || 0,
            sequence: option.sequence,
          }));
        }
      }

      // Re-initialize questions for GRID and TEXTFIELD types
      if (
        updatedItemBlock.type === 'GRID' &&
        updatedItemBlock.questions &&
        updatedItemBlock.questions.length > 0
      ) {
        const rowQuestions = updatedItemBlock.questions
          .filter((q) => !q.isHeader)
          .sort((a, b) => a.sequence - b.sequence);

        if (rowQuestions.length > 0) {
          gridRowQuestions.value = rowQuestions.map((q, idx) => ({
            id: q.id,
            label: q.questionText || '',
            value: `question${idx + 1}`,
            sequence: q.sequence,
          }));
        }
      }

      if (
        updatedItemBlock.type === 'TEXTFIELD' &&
        updatedItemBlock.questions &&
        updatedItemBlock.questions.length > 0
      ) {
        textInput.value = updatedItemBlock.questions[0]?.questionText || '';
      }

      console.log('✅ [STORE] Store re-initialization completed');
    };

    return {
      //state
      radioOptions,
      optionSelectedOption,
      checkboxOptions,
      checkboxSelectedOptions,
      gridRowQuestions,
      gridColumnOptions,
      textInput,
      draggedIndex,
      draggedSection,
      hoveredIndex,

      // Actions
      updateOption,
      addOption,
      addOtherOption,
      removeOption,
      addRowQuestion,
      addColumnOption,
      removeRowQuestion,
      removeColumnOption,
      updateRowQuestion,
      updateColumnOption,
      startDrag,
      handleDragStart,
      hoverRow,
      drop,
      gridDrop,
      endDrag,

      // Extract methods
      extractOptions,
      extractQuestions,
      extractItemBlockData,

      // Helper methods for ID management
      findOptionById,
      updateOptionId,
      syncOptionSequences,
      syncGridColumnSequences,

      // Options synchronization for type changes
      syncOptionsFromRadioToCheckbox,
      syncOptionsFromCheckboxToRadio,

      // Re-initialization method
      reinitializeWithUpdatedData,
    };
  });
