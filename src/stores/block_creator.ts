import { defineStore } from 'pinia';
import type { ItemBlock, Assessment } from 'src/types/models';
import type { DataResponse } from 'src/types/data';
import { ref, computed } from 'vue';
import { AssessmentService } from 'src/services/asm/assessmentService';

// BlockData interface for UI compatibility
// export interface BlockData {
//   id: number;
//   type: 'header' | 'item' | 'image';
//   questionText?: string;
//   selectedType?: string;
//   isSectionStart?: boolean;
//   imageData?: string;
//   isRequired?: boolean;
// }

type DOMRefElement = Element | null;

// Allowed types for AnswerItemBlockType
const allowedTypes: string[] = ['RADIO', 'CHECKBOX', 'TEXTFIELD', 'GRID'];

export const useBlockCreatorStore = defineStore('blockCreator', () => {
  // State
  const blocks = ref<ItemBlock[]>([]);
  const blockRefs: Record<number, DOMRefElement> = {};
  const selectedBlock = ref<ItemBlock | null>(null);
  const selectedBlockId = ref<string>();

  // Assessment management state (migrated from form.ts)
  const meta = ref<DataResponse<Assessment> | null>(null);
  const assessments = ref<Assessment[]>([]);
  const currentAssessment = ref<Assessment | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const page = ref(1);
  const limit = ref(5);
  const search = ref('');

  // Global state for duplicate dialog (migrated from formItemCreateStore.ts)
  const duplicateDialog = ref(false);

  // let nextId = 1;

  // ID generation utilities
  const lastUsedQuestionId = ref(0);
  const lastUsedOptionId = ref(0);

  function generateQuestionId(): number {
    lastUsedQuestionId.value++;
    return lastUsedQuestionId.value;
  }
  function generateOptionId(): number {
    lastUsedOptionId.value++;
    return lastUsedOptionId.value;
  }

  // Block operations
  function addBlock(item: ItemBlock, index: number) {
    blocks.value.splice(index + 1, 0, item);
  }

  function appendBlock(item: ItemBlock) {
    blocks.value.push(item);
  }

  function updateBlocksOrder(newBlocks: ItemBlock[]) {
    // Update the blocks array with the new order
    blocks.value = newBlocks;

    // Update sequence numbers to match the new order
    blocks.value.forEach((block, index) => {
      block.sequence = index + 1;
    });
  }

  function setSection(value: number, index: number) {
    blocks.value[index] = {
      ...blocks.value[index],
      section: value,
    } as ItemBlock;
  }

  function updateBlock(item: ItemBlock, index: number) {
    blocks.value[index] = item;
  }

  function deleteBlock(index: number) {
    blocks.value.splice(index, 1);
  }

  function duplicateBlock(source: ItemBlock, index: number) {
    const newBlock: ItemBlock = {
      ...source,
    };
    blocks.value.splice(index + 1, 0, newBlock);
    return newBlock;
  }

  // Section helpers
  function isSectionBlock(index: number): boolean {
    const current = blocks.value[index];
    if (!current) return false;

    // A block is considered a section start if:
    // 1. It's a HEADER block, AND
    // 2. It's either the first block OR has a different section number than the previous block
    if (current.type === 'HEADER') {
      if (index === 0) return true; // First block is always a section start

      const previous = blocks.value[index - 1];
      if (!previous) return true;

      // Check if this header starts a new section
      return current.section !== previous.section;
    }

    return false;
  }

  const totalSections = computed(() => {
    // Count unique section numbers from all blocks
    const sectionNumbers = new Set(blocks.value.map((block) => block.section));
    return sectionNumbers.size;
  });

  function getSectionNumber(index: number): number {
    const current = blocks.value[index];
    if (!current) return 1;

    // Return the section number of the current block
    return current.section || 1;
  }

  // Block refs for scrolling/focus
  function setBlockRef(id: number, el: DOMRefElement) {
    blockRefs[id] = el;
  }
  function getBlockRef(id: number) {
    return blockRefs[id];
  }

  // Initialization helpers
  function resetBlocks(initialBlocks: ItemBlock[]) {
    blocks.value = initialBlocks;
  }

  function initializeBlocks(initialBlocks: ItemBlock[]) {
    blocks.value = [...initialBlocks];
  }

  function isAnswerItemBlockType(type: string): boolean {
    return allowedTypes.includes(type);
  }

  // Assessment management functions (migrated from form.ts)
  const fetchAssessmentById = async (id: number) => {
    loading.value = true;
    error.value = null;
    try {
      const res = await new AssessmentService('evaluate').fetchOne(id);
      if (res) {
        currentAssessment.value = res;
        // Sync blocks with assessment
        if (res.itemBlocks) {
          blocks.value = res.itemBlocks;
        }
      }
    } catch (err: unknown) {
      error.value = err instanceof Error ? err.message : 'ไม่สามารถโหลดแบบทดสอบได้';
    } finally {
      loading.value = false;
    }
  };

  const addAssessment = async (assessmentData: Partial<Assessment>): Promise<Assessment> => {
    const res = await new AssessmentService('evaluate').createOne(assessmentData);
    assessments.value.push(res);
    currentAssessment.value = res;
    return res;
  };

  const updateAssessment = async (id: number, assessmentData: Assessment): Promise<Assessment> => {
    const res = await new AssessmentService('evaluate').updateOne(id, assessmentData);
    const index = assessments.value.findIndex((q) => q.id === id);
    if (index !== -1) assessments.value[index] = res;
    if (currentAssessment.value?.id === id) currentAssessment.value = res;
    return res;
  };

  const removeAssessment = async (id: number): Promise<void> => {
    await new AssessmentService('evaluate').deleteOne(id);
    assessments.value = assessments.value.filter((q) => q.id !== id);
    if (currentAssessment.value?.id === id) {
      currentAssessment.value = null;
    }
  };

  // ID Tracking Helpers (migrated from form.ts)
  const getAssessmentId = (): number | null => {
    return currentAssessment.value?.id || null;
  };

  const getItemBlockById = (id: number) => {
    return currentAssessment.value?.itemBlocks?.find((block) => block.id === id) || null;
  };

  const getHeaderBlockId = (): number | null => {
    const headerBlock = currentAssessment.value?.itemBlocks?.find(
      (block) => block.type === 'HEADER',
    );
    return headerBlock?.id || null;
  };

  const getRadioBlockId = (): number | null => {
    const radioBlock = currentAssessment.value?.itemBlocks?.find((block) => block.type === 'RADIO');
    return radioBlock?.id || null;
  };

  const getAllItemBlockIds = (): number[] => {
    return currentAssessment.value?.itemBlocks?.map((block) => block.id) || [];
  };

  // Validation Helpers (migrated from form.ts)
  const validateIds = (): { valid: boolean; missing: string[] } => {
    const missing: string[] = [];

    if (!currentAssessment.value?.id) {
      missing.push('assessmentId');
    }

    if (!currentAssessment.value?.itemBlocks || currentAssessment.value.itemBlocks.length === 0) {
      missing.push('itemBlocks');
    } else {
      currentAssessment.value.itemBlocks.forEach((block, index) => {
        if (!block.id) {
          missing.push(`itemBlock[${index}].id`);
        }
        if (!block.assessmentId) {
          missing.push(`itemBlock[${index}].assessmentId`);
        }
      });
    }

    return {
      valid: missing.length === 0,
      missing,
    };
  };

  // Enhanced Validation for Block Deletion (migrated from form.ts)
  const validateBlockDeletion = (blockId: number): { canDelete: boolean; issues: string[] } => {
    const issues: string[] = [];

    if (!currentAssessment.value) {
      issues.push('No current assessment loaded');
      return { canDelete: false, issues };
    }

    if (!blockId) {
      issues.push('Invalid block ID provided');
      return { canDelete: false, issues };
    }

    const targetBlock = currentAssessment.value.itemBlocks?.find((block) => block.id === blockId);
    if (!targetBlock) {
      issues.push(`Block with ID ${blockId} not found in current assessment`);
      return { canDelete: false, issues };
    }

    // Additional validation for header blocks
    if (targetBlock.type === 'HEADER' && !targetBlock.headerBody) {
      issues.push('Header block missing headerBody data');
    }

    // Check for orphaned references
    if (targetBlock.assessmentId !== currentAssessment.value.id) {
      issues.push(
        `Block assessmentId (${targetBlock.assessmentId}) does not match current assessment ID (${currentAssessment.value.id})`,
      );
    }

    return {
      canDelete: issues.length === 0,
      issues,
    };
  };

  // Post-Deletion Validation (migrated from form.ts)
  const validatePostDeletion = (deletedBlockId: number): { success: boolean; issues: string[] } => {
    const issues: string[] = [];

    if (!currentAssessment.value) {
      issues.push('No current assessment loaded');
      return { success: false, issues };
    }

    // Check if the block still exists in the assessment
    const blockStillExists = currentAssessment.value.itemBlocks?.some(
      (block) => block.id === deletedBlockId,
    );
    if (blockStillExists) {
      issues.push(`Block with ID ${deletedBlockId} still exists in assessment after deletion`);
    }

    // Check for any orphaned references
    const orphanedQuestions = currentAssessment.value.itemBlocks?.some((block) =>
      block.questions?.some((question) => question.itemBlockId === deletedBlockId),
    );
    if (orphanedQuestions) {
      issues.push(`Found orphaned questions referencing deleted block ID ${deletedBlockId}`);
    }

    const orphanedOptions = currentAssessment.value.itemBlocks?.some((block) =>
      block.options?.some((option) => option.itemBlockId === deletedBlockId),
    );
    if (orphanedOptions) {
      issues.push(`Found orphaned options referencing deleted block ID ${deletedBlockId}`);
    }

    return {
      success: issues.length === 0,
      issues,
    };
  };

  // Assessment data conversion
  function getAssessmentData() {
    return {
      blocks: blocks.value,
      totalBlocks: blocks.value.length,
      totalSections: totalSections.value,
    };
  }

  // Expose API
  return {
    // Original block creator functionality
    isAnswerItemBlockType,
    blocks,
    addBlock,
    appendBlock,
    updateBlocksOrder,
    setSection,
    updateBlock,
    deleteBlock,
    duplicateBlock,
    isSectionBlock,
    totalSections,
    getSectionNumber,
    setBlockRef,
    getBlockRef,
    resetBlocks,
    initializeBlocks,
    generateQuestionId,
    generateOptionId,
    selectedBlock,
    selectedBlockId,
    getAssessmentData,

    // Assessment management (migrated from form.ts)
    assessments,
    currentAssessment,
    loading,
    error,
    meta,
    page,
    limit,
    search,
    fetchAssessmentById,
    addAssessment,
    updateAssessment,
    removeAssessment,

    // ID tracking helpers (migrated from form.ts)
    getAssessmentId,
    getItemBlockById,
    getHeaderBlockId,
    getRadioBlockId,
    getAllItemBlockIds,
    validateIds,

    // Enhanced validation helpers (migrated from form.ts)
    validateBlockDeletion,
    validatePostDeletion,

    // Global state (migrated from formItemCreateStore.ts)
    duplicateDialog,
  };
});
