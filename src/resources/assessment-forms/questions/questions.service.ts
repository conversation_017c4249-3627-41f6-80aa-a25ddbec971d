import { Injectable, NotFoundException } from '@nestjs/common';

import { InjectRepository } from '@nestjs/typeorm';
import { Question } from '../entities/question.entity';
import { Repository } from 'typeorm';
import { ItemBlock } from '../entities/item-block.entity';
import { CreateQuestionDto } from '../dto/creates/create-question.dto';
import { UpdateQuestionDto } from '../dto/updates/update-question.dto';
import { FileUploadService } from '../utils/file-upload.service';

@Injectable()
export class QuestionsService {
  constructor(
    @InjectRepository(Question)
    private readonly questionRepository: Repository<Question>,
    @InjectRepository(ItemBlock)
    private readonly itemBlockRepository: Repository<ItemBlock>,
    private readonly fileUploadService: FileUploadService,
  ) {}

  async getNextSequence(itemBlockId: number): Promise<number> {
    const lastQuestion = await this.questionRepository
      .createQueryBuilder('question')
      .where('question.itemBlockId = :itemBlockId', { itemBlockId })
      .orderBy('question.sequence', 'DESC')
      .getOne();

    return lastQuestion ? lastQuestion.sequence + 1 : 1;
  }

  async getMaxSectionNumber(assessmentId: number): Promise<number> {
    const lastSection = await this.itemBlockRepository
      .createQueryBuilder('itemBlock')
      .where('itemBlock.assessmentId = :assessmentId', { assessmentId })
      .orderBy('itemBlock.section', 'DESC')
      .getOne();

    return lastSection ? lastSection.section + 1 : 1;
  }

  async create(
    createQuestionDto: CreateQuestionDto,
    file?: Express.Multer.File,
  ) {
    const {
      itemBlockId,
      acceptFile,
      sizeLimit,
      uploadLimit,
      score,
      imagePath,
      questionText,
      sequence,
      ...rest
    } = createQuestionDto;
    const itemBlock = await this.itemBlockRepository.findOne({
      where: { id: itemBlockId },
    });
    if (!itemBlock) {
      throw new NotFoundException('ItemBlock with the given ID is not found');
    }
    let finalImagePath = imagePath?.trim() || null;
    if (file) {
      finalImagePath = await this.fileUploadService.uploadAndGetImagePath(file);
      console.log('Uploaded Image Path:', finalImagePath);
    }
    const question = this.questionRepository.create({
      ...rest,
      sizeLimit: sizeLimit === undefined ? null : sizeLimit,
      uploadLimit: uploadLimit === undefined ? null : uploadLimit,
      imagePath: finalImagePath,
      itemBlock,
      score: score === undefined ? null : score,
      questionText: questionText?.trim() || '',
      acceptFile: acceptFile?.trim() || null,
      sequence: await this.getNextSequence(itemBlockId),
    });

    const saved = await this.questionRepository.save(question);

    return {
      id: saved.id,
      itemBlockId: saved.itemBlock.id,
      questionText: saved.questionText,
      imagePath: saved.imagePath,
      isHeader: saved.isHeader,
      sequence: saved.sequence,
      sizeLimit: saved.sizeLimit,
      acceptFile: saved.acceptFile,
      uploadLimit: saved.uploadLimit,
    };
  }

  findAll() {
    return this.questionRepository.find({
      relations: ['itemBlock', 'responses'],
    });
  }

  findOne(id: number) {
    return this.questionRepository.findOne({
      where: { id },
      relations: ['itemBlock', 'responses'],
    });
  }

  async update(
    id: number,
    updateQuestionDto: UpdateQuestionDto,
    file?: Express.Multer.File,
  ) {
    const {
      itemBlockId,
      sizeLimit,
      uploadLimit,
      acceptFile,
      imagePath,
      score,
      ...rest
    } = updateQuestionDto;

    const question = await this.questionRepository.findOne({
      where: { id },
      relations: ['itemBlock', 'responses'],
    });

    if (!question) {
      throw new NotFoundException(`Question with ID ${id} not found`);
    }

    if (itemBlockId !== undefined) {
      const itemBlock = await this.itemBlockRepository.findOne({
        where: { id: itemBlockId },
      });
      if (!itemBlock) {
        throw new NotFoundException(
          `ItemBlock with ID ${itemBlockId} not found`,
        );
      }
      question.itemBlock = itemBlock;
    }

    let finalImagePath = question.imagePath;
    if (file) {
      finalImagePath = await this.fileUploadService.uploadAndGetImagePath(file);
      if (question.imagePath) {
        await this.fileUploadService.deleteFileByUrl(question.imagePath);
      }
    } else if (imagePath !== undefined) {
      finalImagePath = imagePath?.trim() || null;
      if (finalImagePath === null && question.imagePath) {
        await this.fileUploadService.deleteFileByUrl(question.imagePath);
      }
    }
    question.imagePath = finalImagePath;

    if (sizeLimit !== undefined) {
      question.sizeLimit = Number(sizeLimit) || null;
    }

    if (uploadLimit !== undefined) {
      question.uploadLimit = Number(uploadLimit) || null;
    }

    if (acceptFile !== undefined) {
      question.acceptFile = acceptFile?.trim() || null;
    }

    if (score !== undefined) {
      question.score = Number(score) || null;
    }

    Object.assign(question, rest);

    const updated = await this.questionRepository.save(question);

    return {
      id: updated.id,
      itemBlockId: updated.itemBlock.id,
      questionText: updated.questionText,
      imagePath: updated.imagePath,
      isHeader: updated.isHeader,
      sequence: updated.sequence,
      sizeLimit: updated.sizeLimit,
      acceptFile: updated.acceptFile,
      uploadLimit: updated.uploadLimit,
      responses: updated.responses,
      score: updated.score,
      ...rest,
    };
  }

  async remove(id: number) {
    const question = await this.questionRepository.findOne({ where: { id } });
    if (!question) {
      throw new NotFoundException(`Question with ID ${id} not found`);
    }
    if (question.imagePath) {
      await this.fileUploadService.deleteFileByUrl(question.imagePath);
    }
    await this.questionRepository.delete({ id });
  }

  async removeByItemBlockId(itemBlockId: number): Promise<void> {
    await this.questionRepository.delete({ itemBlockId });
  }
}
