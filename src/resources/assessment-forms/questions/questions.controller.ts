import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { QuestionsService } from './questions.service';
import { ApiTags, ApiOperation, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { CreateQuestionDto } from '../dto/creates/create-question.dto';
import { UpdateQuestionDto } from '../dto/updates/update-question.dto';
import { AnyFilesInterceptor, FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { v4 as uuidv4 } from 'uuid';

@ApiTags('Questions')
@Controller('questions')
export class QuestionsController {
  constructor(private readonly questionsService: QuestionsService) {}

  @Post()
  @ApiOperation({
    summary: 'สร้างคำถามใหม่',
    description: 'สร้างคำถาม (Evaluate) ใหม่ตาม template ที่กำหนด',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'ข้อมูลสำหรับสร้างคำถามใหม่ (แบบฟอร์ม)',
    schema: {
      type: 'object',
      properties: {
        questionText: { type: 'string', example: 'คำถามตัวอย่าง' },
        imagePath: {
          type: 'string',
          format: 'binary',
        },
        isHeader: { type: 'boolean', example: false },
        sequence: { type: 'integer', example: 1 },
        sizeLimit: { type: 'integer', example: 1024, default: 0 },
        acceptFile: { type: 'string', example: 'jpg,png', default: null },
        uploadLimit: { type: 'integer', example: 5, default: 0 },
        itemBlockId: { type: 'integer', example: 1 },
        score: { type: 'integer', example: 10, default: null },
      },
      required: ['itemBlockId'],
    },
  })
  @UseInterceptors(
    FileInterceptor('imagePath', {
      storage: diskStorage({
        destination: './uploaded_file',
        filename: (req, file, cb) => {
          const ext = extname(file.originalname);
          const name = uuidv4();
          cb(null, name + ext);
        },
      }),
    }),
  )
  @ApiTags('Create-Question')
  create(
    @Body() createQuestionDto: CreateQuestionDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.questionsService.create(createQuestionDto, file);
  }

  @Get()
  findAll() {
    return this.questionsService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.questionsService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'อัปเดตคำถาม',
    description: 'อัปเดตคำถาม (Evaluate) ตาม template ที่กำหนด',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'ข้อมูลสำหรับอัปเดตคำถาม (แบบฟอร์ม)',
    schema: {
      type: 'object',
      properties: {
        questionText: { type: 'string', example: 'คำถามตัวอย่าง' },
        imagePath: {
          type: 'string',
          format: 'binary',
        },
        isHeader: { type: 'boolean', example: false },
        sequence: { type: 'integer', example: 1 },
        sizeLimit: { type: 'integer', example: 1024, default: 0 },
        acceptFile: { type: 'string', example: 'jpg,png', default: null },
        uploadLimit: { type: 'integer', example: 5, default: 0 },
        itemBlockId: { type: 'integer', example: 1 },
        score: { type: 'integer', example: 10, default: null },
      },
      required: ['itemBlockId'],
    },
  })
  @UseInterceptors(
    FileInterceptor('imagePath', {
      storage: diskStorage({
        destination: './uploaded_file',
        filename: (req, file, cb) => {
          const ext = extname(file.originalname);
          const name = uuidv4();
          cb(null, name + ext);
        },
      }),
    }),
  )
  update(
    @Param('id') id: string,
    @Body() updateQuestionDto: UpdateQuestionDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.questionsService.update(+id, updateQuestionDto, file);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.questionsService.remove(+id);
  }
}
