import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import type { UpdateSubmissionDto } from '../dto/updates/update-submission.dto';
import type { CreateSubmissionDto } from '../dto/creates/create-submission.dto';
import { StartQuizDto } from '../dto/start-quiz.dto';
import { Submission } from '../entities/submission.entity';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository, Not, IsNull } from 'typeorm';
import { Assessment } from '../entities/assessment.entity';

@Injectable()
export class SubmissionsService {
  @InjectEntityManager()
  private entityManager: EntityManager;
  @InjectRepository(Submission)
  private submissiobRepository: Repository<Submission>;

  async create(createSubmissionDto: CreateSubmissionDto) {
    const submission = this.submissiobRepository.create({
      assessmentId: createSubmissionDto.assessmentId,
      userId: createSubmissionDto.userId,
      startAt: new Date(),
      endAt: null,
    });

    return await this.submissiobRepository.save(submission); // ← ต้อง save
  }

  findAll() {
    return `This action returns all submissions`;
  }

  findOne(id: number) {
    return this.submissiobRepository.findOne({
      relations: ['responses'],
    });
  }

  findDraftByAsmIdAndUserId(asmId: number, userId: number) {
    return this.submissiobRepository.findOne({
      where: {
        assessmentId: asmId,
        userId: userId,
        endAt: null,
      },
      relations: ['responses'],
    });
  }

  update(id: number, updateSubmissionDto: UpdateSubmissionDto) {
    return `This action updates a #${id} submission`;
  }

  remove(id: number) {
    return `This action removes a #${id} submission`;
  }

  async startAssessment(startQuizDto: StartQuizDto) {
    const { linkUrl, userId } = startQuizDto;
    const now = new Date();

    console.log(
      `🚀 Starting assessment for user ${userId} with link: ${linkUrl}`,
    );

    // 🔍 Step 1: Find assessment by link
    const assessment = await this.entityManager.findOne(Assessment, {
      where: { linkURL: linkUrl },
    });

    if (!assessment) {
      console.log(`❌ Assessment not found for link: ${linkUrl}`);
      throw new NotFoundException(
        `Assessment not found for the provided link: ${linkUrl}`,
      );
    }

    console.log(
      `✅ Found assessment: ${assessment.id}, name: ${assessment.name}, submitLimit: ${assessment.submitLimit}, timeout: ${assessment.timeout}s`,
    );

    // 🕐 Step 2: Check if assessment is still open (Assessment Availability)
    if (assessment.endAt && assessment.endAt < now) {
      const endAtFormatted = assessment.endAt.toLocaleString();
      console.log(
        `❌ Assessment is closed. EndAt: ${endAtFormatted}, Now: ${now.toLocaleString()}`,
      );
      throw new BadRequestException(
        `Assessment has ended on ${endAtFormatted}. No new submissions are allowed.`,
      );
    }

    // � Step 3: Check for existing active submission
    const activeSubmission = await this.entityManager.findOne(Submission, {
      where: {
        assessmentId: assessment.id,
        userId,
        submitAt: IsNull(), // Not yet submitted
      },
    });

    console.log(
      `🔍 Active submission check: ${activeSubmission ? `Found (ID: ${activeSubmission.id})` : 'Not found'}`,
    );

    // 📋 Step 4: Handle existing active submission
    if (activeSubmission) {
      console.log(
        `📋 Found existing active submission: ${activeSubmission.id}`,
      );
      console.log(
        `⏰ Submission EndAt: ${activeSubmission.endAt?.toLocaleString()}, Current Time: ${now.toLocaleString()}`,
      );

      // Check if the active submission has timed out (Fixed condition)
      if (activeSubmission.endAt && activeSubmission.endAt < now) {
        const timeoutFormatted = activeSubmission.endAt.toLocaleString();
        console.log(
          `❌ Active submission has timed out. EndAt: ${timeoutFormatted}`,
        );
        throw new BadRequestException(
          `Your previous submission has timed out at ${timeoutFormatted}. Please start a new attempt if allowed.`,
        );
      }

      console.log(
        `✅ Returning existing active submission: ${activeSubmission.id}`,
      );
      return activeSubmission;
    }

    // 📊 Step 5: Check submission limit for new submissions
    if (assessment.submitLimit > 0) {
      const submittedCount = await this.entityManager.count(Submission, {
        where: {
          assessmentId: assessment.id,
          userId,
          submitAt: Not(IsNull()), // Count only completed submissions
        },
      });

      console.log(
        `📊 Submission limit check: ${submittedCount}/${assessment.submitLimit} attempts used`,
      );

      if (submittedCount >= assessment.submitLimit) {
        console.log(
          `❌ Submission limit reached: ${submittedCount}/${assessment.submitLimit}`,
        );
        throw new ForbiddenException(
          `Assessment submission limit reached. You have already completed ${submittedCount} out of ${assessment.submitLimit} allowed attempts.`,
        );
      }
    }

    // 🆕 Step 6: Create new submission for new user or user within limits
    console.log(`🆕 Creating new submission for user ${userId}`);

    const endAt =
      assessment.timeout > 0
        ? new Date(now.getTime() + assessment.timeout * 1000)
        : null; // No time limit if timeout is 0

    const submission = this.entityManager.create(Submission, {
      assessmentId: assessment.id,
      userId,
      startAt: now,
      endAt,
    });

    const savedSubmission = await this.entityManager.save(
      Submission,
      submission,
    );

    console.log(`✅ New submission created successfully:`);
    console.log(`   - Submission ID: ${savedSubmission.id}`);
    console.log(`   - Started At: ${savedSubmission.startAt.toLocaleString()}`);
    console.log(
      `   - Will End At: ${savedSubmission.endAt?.toLocaleString() || 'No time limit'}`,
    );

    return savedSubmission;
  }

  async submitAssessment(submissionId: number) {
    const submission = await this.entityManager.findOne(Submission, {
      where: { id: submissionId },
    });
    if (!submission) {
      throw new NotFoundException('Submission not found');
    }

    // validate submission is not submitted yet
    if (submission.submitAt) {
      throw new BadRequestException('Submission already submitted');
    }
    submission.endAt = new Date();
    submission.submitAt = new Date();
    return this.entityManager.save(Submission, submission);
  }

  async getQuizScore(submissionId: number) {
    const submission = await this.entityManager.findOne(Submission, {
      where: { id: submissionId },
      relations: {
        responses: { question: true, selectedOption: true },
        assessment: true,
      },
    });

    if (!submission) {
      throw new NotFoundException('Submission not found');
    }

    let score = 0;
    for (const response of submission.responses) {
      if (response.selectedOption) {
        console.log('optionValue', response.selectedOption);
        const optionValue = response.selectedOption.value || 0;

        score += optionValue;
      }
    }

    // Calculate used time in seconds
    const usedTimeMs =
      submission.endAt.getTime() - submission.startAt.getTime();
    const usedTime = Math.floor(usedTimeMs / 1000);

    // Convert to human-readable format hours:minutes:seconds
    const hours = Math.floor(usedTime / 3600);
    const minutes = Math.floor((usedTime % 3600) / 60);
    const seconds = usedTime % 60;
    const usedTimeFormatted = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

    return {
      score,
      totalScore: submission.assessment.totalScore,
      usedTimeFormatted, // in HH:MM:SS format
      submitTime: submission.endAt,
    };
  }
}
