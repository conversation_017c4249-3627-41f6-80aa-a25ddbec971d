import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OptionsController } from './options.controller';

import { ItemBlock } from '../entities/item-block.entity';
import { Question } from '../entities/question.entity';
import { FileUploadService } from '../utils/file-upload.service';
import { ItemBlocksModule } from '../item-blocks/item-blocks.module';
import { Option } from '../entities/option.entity';
import { ItemBlocksService } from '../item-blocks/item-blocks.service';
import { HeaderBody } from '../entities/header-body.entity';
import { HeaderBodiesModule } from '../header-bodies/header-bodies.module';
import { ImageBody } from '../entities/image-body.entity';
import { ImageBodiesModule } from '../image-bodies/image-bodies.module';
import { Response } from '../entities/response.entity';
import { Submission } from '../entities/submission.entity';
import { Assessment } from '../entities/assessment.entity';
import { OptionsService } from './options.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Assessment,
      Option,
      ItemBlock,
      Question,
      HeaderBody,
      ImageBody,
      Response,
      Submission,
    ]),
    forwardRef(() => ItemBlocksModule),
    HeaderBodiesModule,
    ImageBodiesModule,
  ],
  controllers: [OptionsController],
  providers: [FileUploadService, OptionsService],
  exports: [FileUploadService, OptionsService],
})
export class OptionsModule {}
