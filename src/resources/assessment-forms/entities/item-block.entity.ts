import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { ItemBlockType } from '../enums/item-block-type.enum';
import { Assessment } from './assessment.entity';
import { Option } from './option.entity';
import { Question } from './question.entity';
import { HeaderBody } from './header-body.entity';
import { ImageBody } from './image-body.entity';

@Entity('item_blocks')
export class ItemBlock {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  sequence: number;

  @Column({ default: 1 })
  section: number;

  @Column({
    type: 'enum',
    enum: ItemBlockType,
  })
  type: ItemBlockType;

  @Column({ default: false })
  isRequired: boolean;

  @Column({ nullable: true })
  assessmentId: number;

  @ManyToOne(() => Assessment, (assessment) => assessment.itemBlocks, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'assessmentId' })
  assessment: Assessment;

  @OneToOne(() => HeaderBody, (headerBody) => headerBody.itemBlock, {
    cascade: true,
  })
  headerBody: HeaderBody;

  @OneToOne(() => ImageBody, (imageBody) => imageBody.itemBlock, {
    cascade: true,
  })
  imageBody: ImageBody;

  @OneToMany(() => Option, (option) => option.itemBlock, {
    cascade: true,
  })
  options: Option[];

  @OneToMany(() => Question, (question) => question.itemBlock, {
    cascade: true,
  })
  questions: Question[];
}
