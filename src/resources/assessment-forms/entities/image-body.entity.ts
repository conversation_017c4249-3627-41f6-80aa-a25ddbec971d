import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON>umn,
  OneToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'typeorm';
import { ItemBlock } from './item-block.entity';

@Entity('image_bodies')
export class ImageBody {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  imageText: string | null;

  @Column({ nullable: true, type: 'text' })
  imagePath: string | null;

  @Column({ nullable: true })
  imageWidth: number;

  @Column({ nullable: true })
  imageHeight: number;

  @Column()
  itemBlockId: number;

  @OneToOne(() => ItemBlock, (itemBlock) => itemBlock.imageBody, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'itemBlockId' })
  itemBlock: ItemBlock;
}
