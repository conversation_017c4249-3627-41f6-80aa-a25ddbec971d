import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { Type } from 'class-transformer';

export class CreateResponseDto {
  @ApiProperty()
  @IsNotEmpty()
  @Type(() => Number) // 👈 เพิ่มเพื่อแปลงเป็น number
  @IsNumber()
  submissionId: number;

  @ApiProperty()
  @IsNotEmpty()
  @Type(() => Number)
  @IsNumber()
  questionId: number;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  responseId?: number;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  selectedOptionId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  answerText?: string;
}
