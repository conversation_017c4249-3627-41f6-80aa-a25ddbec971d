import { boot } from 'quasar/wrappers';
import axios, { type AxiosInstance } from 'axios';
import type { Router } from 'vue-router';

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $axios: AxiosInstance;
  }
}

const api = axios.create({
  baseURL: import.meta.env.VITE_BACKEND_URL || '',
});

let routerInstance: Router | null = null;

api.defaults.headers.common['Access-Control-Origin'] = '*';
api.defaults.headers.common['Access-Control-Allow-Credentials'] = true;

api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error instanceof Error ? error : new Error(String(error)));
  },
);

api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      if (
        error.response?.data?.message &&
        error.response.data.message.includes('ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง')
      ) {
        return Promise.reject(error instanceof Error ? error : new Error(String(error)));
      }

      // Session expired - clear tokens and redirect to login
      localStorage.removeItem('access_token');
      localStorage.removeItem('perms');
      localStorage.removeItem('hasVisited');

      // Save current path for redirect after login
      const currentPath = window.location.pathname + window.location.search;
      if (currentPath !== '/login') {
        localStorage.setItem('redirectAfterLogin', currentPath);
      }

      if (routerInstance) {
        routerInstance.push('/login').catch(() => {
          window.location.href = '/login';
        });
      } else {
        window.location.href = '/login';
      }

      return Promise.reject(error instanceof Error ? error : new Error('Session expired'));
    }
    return Promise.reject(error instanceof Error ? error : new Error(String(error)));
  },
);

export default boot(({ app, router }) => {
  app.config.globalProperties.$axios = api;

  routerInstance = router;
});

export { api };
