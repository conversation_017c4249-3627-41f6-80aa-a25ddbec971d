// permission.guard.ts
import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(private readonly requiredPermission: string) {}
  // > 1
  // constructor(private readonly requiredPermissions: string[]) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || !user.roles) {
      throw new ForbiddenException('No roles assigned.');
    }

    const hasPermission = user.psnPermissions.some(
     (perm: any) => perm.perNameEn === this.requiredPermission,
    );

    // > 1
    // const hasPermission = user.permissions.some((p) =>
    //     this.requiredPermissions.includes(p.perNameEn)
    // );

    if (!hasPermission) {
      throw new ForbiddenException('You do not have permission!');
    }

    return true;
  }
}
