import { CACHE_MANAGER } from '@nestjs/cache-manager';
import {
  Inject,
  Injectable,
  // NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
// import axios from 'axios';
import { Cache } from 'cache-manager';
import { ApiService } from 'src/api/api.service';

import { GraylogService } from 'src/graylog/graylog.service';
import { UtilsService } from 'src/utils/utils.service';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User } from 'src/resources/users/entities/user.entity';

@Injectable()
export class AuthService {
  constructor(
    private configService: ConfigService,
    private jwtService: JwtService,
    private apiService: ApiService,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
    private utilsService: UtilsService,
    private readonly graylogService: GraylogService,
    @InjectRepository(User)
    private userRepo: Repository<User>,
  ) {}

  async signIn(username: string, password: string): Promise<any> {
    const oneWeekInSeconds = 7 * 24 * 60 * 60;
    // const apiName = 'loginBuu';
    const encryptionKey = 'E13qDGn!Z|"38y/BUvFjl$cA-vs4)P';

    console.log('Raw username:', username);
    console.log('Raw password:', password);
  
    // const decryptedUsername = username
    // const decryptedPassword =password
     
    const decryptedUsername = await this.utilsService.decryptString(
      'loginBuu_username',
      username,
    );
    const decryptedPassword = await this.utilsService.decryptString(
      'loginBuu_password',
      password,
    );
    console.log(
      'decryptedUsername',
      decryptedUsername,
      'decryptedPassword',
      decryptedPassword,
    );
    let userDisplayName = '';

    const user = await this.userRepo.findOne({
      where: { email: decryptedUsername },
      relations: {
        roles: {
          permissions: true,
        },
      },
      
    });

    if (!user) {
      throw new UnauthorizedException('ไม่พบบัญชีผู้ใช้');
    }

    // decrypt password bcrypt
    const passwordMatch = await bcrypt.compare(
      decryptedPassword,
      user.password,
    );

    if (!passwordMatch) {
      throw new UnauthorizedException('ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง');
    }
    console.log('user', user);

    userDisplayName = user.name;

    return this.createAuthToken(user, encryptionKey, oneWeekInSeconds);
  }

  private async createAuthToken(
    user: User,
    encryptionKey: string,
    ttlSeconds: number,
  ) {
    const psnPermissions = user.roles.flatMap((role) =>
    (role.permissions ?? [])
      .filter(permission => permission.status === true)
      .map((permission) => ({
        perId: permission.id,
        // perNameTh: permission.perNameTh,
        perNameEn: permission.nameEn,
      })),
  );
    console.log('psnPermissions', psnPermissions);

    const userPayload = {
      id: user.id,
      name: user.name,
      email: user.email,
      roles: user.roles.map((role) => ({
        id: role.id,
        name: role.name,
      })),
      psnPermissions,
    };

    console.log('userPayload', userPayload);

    const encryptedUser = this.utilsService.encryptObject(
      encryptionKey,
      userPayload,
    );

    await this.cacheManager.set(
      `${user.email}_encryptedUser`,
      encryptedUser,
      ttlSeconds,
    );

    // private async createAuthToken(
    //   username: string,
    //   user: any,
    //   encryptionKey: string,
    //   ttlSeconds: number,
    // ) {
    //   const encryptedUser = this.utilsService.encryptObject(encryptionKey, user);
    //   await this.cacheManager.set(
    //     ${username}_encryptedUser,
    //     encryptedUser,
    //     ttlSeconds,
    //   );

    //   const token = await this.jwtService.signAsync({
    //     sub: encryptedUser,
    //   });
    //   await this.cacheManager.set(${username}_encryptedTK, token, ttlSeconds);

    //   return {
    //     access_token: token,
    //   };
    // }
    const token = await this.jwtService.signAsync({
      sub: encryptedUser,
    });
    await this.cacheManager.set(`${user.email}_encryptedTK`, token, ttlSeconds);

    console.log(token + ' ' + encryptedUser + ' ' + user.email + ' ' + token);
    return {
      access_token: token,
    };
  }

  private async handleSignInError(
    error: any,
    username: string,
    password: string,
    userFullName: string,
    apiName: string,
  ): Promise<void> {
    const logData = {
      efLogRequest: 'Request... POST /auth/loginBuu',
      efLogRequestBody: JSON.stringify({ username, password }),
      efLogResStatusCode: error.response?.status?.toString() || '',
      efLogResMessage: error.toString(),
      efLogReqUser: userFullName,
    };

    await this.graylogService.error(
      `${logData.efLogRequest} ${logData.efLogResMessage}`,
      logData.efLogRequestBody,
      'from_web',
    );

    const errorMsg = error.response?.data?.message;
    const errorType = error.response?.data?.error;

    if (errorType === 'invalid_token' || error.response?.status === 401) {
      await this.apiService.refreshToken(apiName);
      return this.signIn(username, password); // Retry
    } else if (errorMsg === 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง') {
      await this.apiService.sendNoti(
        `${this.configService.get<string>('noti')}${apiName}: ${error}`,
      );
      throw new UnauthorizedException('ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง');
    } else {
      await this.apiService.sendNoti(
        `${this.configService.get<string>('noti')}${apiName}: ${error}`,
      );
      console.error(error);
    }
  }
}
