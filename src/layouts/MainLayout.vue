<script setup lang="ts">
import AppBreadcrumb from 'src/components/AppBreadcrumb.vue';
import MainFooter from 'src/components/MainFooter.vue';
import MainHeader from 'src/components/MainHeader.vue';
import { useRoute } from 'vue-router';
import { computed, watch } from 'vue';
import { useGlobalStore } from 'src/stores/global';
import SystemMenu from 'src/components/SystemMenu.vue';
import type { MenuType } from 'src/types/app';

const route = useRoute();
const globalStore = useGlobalStore();

const quizpath = computed(() => {
  const staticPaths = ['/quiz/new'];
  const dynamicPatterns = [
    /^\/quiz\/\d+$/,
    /^\/quiz\/management\/[^/]+\/responses$/,
    /^\/quiz\/management\/[^/]+\/settings$/,
  ];

  if (staticPaths.some((p) => route.path === p)) return true;
  if (dynamicPatterns.some((pattern) => pattern.test(route.path))) return true;

  return false;
});

const showBreadcrumb = computed(() => {
  const hiddenPaths = ['/quiz/user-quiz'];
  return !hiddenPaths.some((p) => route.path.startsWith(p));
});

const menuType = computed(() => {
  const queryType = route.query.type as MenuType;
  if (queryType) return queryType;

  const path = route.path;
  if (path.startsWith('/evaluate')) return 'form';
  if (path.startsWith('/quiz')) return 'quiz';
  if (path.startsWith('/ums')) return 'ums';

  return 'default';
});

// ดึง quiz ID จาก route params
const currentQuizId = computed(() => {
  const id = route.params.id;
  return Array.isArray(id) ? id[0] : id;
});

// ดึงชื่อแบบทดสอบจาก global store
const currentQuizTitle = computed(() => {
  if (currentQuizId.value) {
    return globalStore.getCurrentQuizTitle(currentQuizId.value);
  }
  return '';
});

const fullBreadcrumbItems = computed(() => globalStore.getBreadcrumbByType(menuType.value));

const breadcrumbItems = computed(() => {
  const currentPath = route.path;
  const result = [];

  if (fullBreadcrumbItems.value.length > 0) {
    const home = fullBreadcrumbItems.value[0];
    if (home) result.push(home);
  }

  for (let i = 1; i < fullBreadcrumbItems.value.length; i++) {
    const item = fullBreadcrumbItems.value[i];
    if (currentPath === item?.link) {
      result.push(item);
      break;
    }
    if (item?.link && currentPath.startsWith(item.link)) {
      result.push(item);
    }
  }

  // เพิ่มชื่อแบบทดสอบลงใน breadcrumb ถ้าอยู่ใน quiz path
  if (quizpath.value && currentQuizTitle.value) {
    result.push({
      title: currentQuizTitle.value,
      icon: 'quiz',
      link: route.path,
    });
  }

  return result;
});

watch(
  menuType,
  (type) => {
    globalStore.setActiveMenuType(type);
  },
  { immediate: true },
);
</script>

<template>
  <q-layout view="hhh lpR fFf">
    <MainHeader :quiz-title="quizpath ? currentQuizTitle : ''" />

    <q-page-container>
      <QuizStatusBar v-if="quizpath" :quiz-title="currentQuizTitle" />
      <QuizMenuTab v-if="quizpath" type="quiz" />

      <SystemMenu v-else :type="menuType" />
      <AppBreadcrumb v-if="showBreadcrumb" :items="breadcrumbItems" />
      <div style="padding: 24px 24px">
        <router-view v-slot="{ Component }">
          <keep-alive :include="['create-evaluate', 'evaluate-settings']">
            <component :is="Component" />
          </keep-alive>
        </router-view>
      </div>
    </q-page-container>
    <MainFooter />
  </q-layout>
</template>

<style scoped></style>
