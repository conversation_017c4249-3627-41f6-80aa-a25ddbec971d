import { applyDecorators } from '@nestjs/common';
import { ApiQuery } from '@nestjs/swagger';

export function DefaultQueryParams() {
  return applyDecorators(
    ApiQuery({
      name: 'sortBy',
      required: false,
      description: 'Field to sort by (e.g., id, name, email)',
    }),
    ApiQuery({
      name: 'order',
      required: false,
      description: 'Sort order (ASC or DESC)',
    }),
    ApiQuery({
      name: 'limit',
      required: true,
      description: 'Number of items per page',
    }),
    ApiQuery({
      name: 'page',
      required: true,
      description: 'Page number to retrieve',
    }),
    ApiQuery({
      name: 'search',
      required: false,
      description: 'Search term to filter results',
    }),
  );
}
