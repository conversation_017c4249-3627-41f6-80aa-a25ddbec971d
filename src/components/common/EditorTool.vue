<template>
  <div>
    <div class="input-wrapper">
      <div
        ref="editableFieldRef"
        class="editable-div"
        :class="{ 'header-font': props.isHeader }"
        contenteditable
        @input="updateInputField"
        @click="showToolbar"
        @focus="handleFocus"
        @blur="handleBlur"
        :data-placeholder="props.label"
      ></div>
    </div>

    <q-slide-transition>
      <div v-show="state.isToolbarVisible" ref="toolbarRef">
        <q-toolbar class="q-gutter-x-xs">
          <template v-for="tool in formattingTools" :key="tool.command">
            <q-btn
              flat
              round
              padding="xs"
              :icon="tool.icon"
              @click="tool.action"
              :aria-label="tool.label"
              :class="{
                'active-format': state.activeFormats.includes(tool.command),
                'btn-tool': true,
              }"
            />
          </template>
        </q-toolbar>
      </div>
    </q-slide-transition>

    <!-- Link Dialog -->
    <q-dialog v-model="showLinkPopup" persistent>
      <q-card class="q-pa-sm" style="min-width: 320px">
        <q-card-section>
          <div class="text-h6">แทรกลิงก์</div>
        </q-card-section>
        <q-card-section>
          <q-form @submit.prevent="applyLink" class="q-gutter-y-md">
            <q-input
              filled
              v-model="linkText"
              label="ข้อความที่แสดง"
              :disable="isTextSelected"
              placeholder="Link Text"
              @keydown.enter.prevent="applyLink"
              tabindex="1"
            >
            </q-input>
            <q-input
              ref="urlInputRef"
              filled
              v-model="linkUrl"
              label="URL"
              placeholder="https://example.com"
              autofocus
              :rules="[(val) => (val && val.trim() !== '') || 'กรุณากรอก URL']"
              @keydown.enter.prevent="applyLink"
              tabindex="0"
            />
          </q-form>
        </q-card-section>
        <q-card-actions align="right" class="q-pt-none">
          <q-btn flat color="grey-7" label="ยกเลิก" @click="showLinkPopup = false" />
          <q-btn color="primary" unelevated label="แทรก" @click="applyLink" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, reactive, computed, defineEmits, watch } from 'vue';

interface FormattingTool {
  icon: string;
  command: string;
  label: string;
  action: () => void;
}

interface EditorState {
  isToolbarVisible: boolean;
  isFocused: boolean;
  hasContent: boolean;
  formattedContent: string;
  activeFormats: string[];
}

// Props and emits
const props = defineProps<{
  label: string;
  initialValue?: string;
  isHeader?: boolean;
}>();

const emit = defineEmits<{
  'update:content': [content: string];
  focus: [event: FocusEvent];
  blur: [event: FocusEvent];
}>();

// Refs
const editableFieldRef = ref<HTMLElement | null>(null);
const toolbarRef = ref<HTMLElement | null>(null);

// Reactive state
const state = reactive<EditorState>({
  isToolbarVisible: false,
  isFocused: false,
  hasContent: false,
  formattedContent: props.initialValue || '',
  activeFormats: [],
});

// Check which formats are currently active
function checkActiveFormats(): void {
  // Reset active formats
  state.activeFormats = [];

  // Check for basic formatting
  if (document.queryCommandState('bold')) {
    state.activeFormats.push('bold');
  }
  if (document.queryCommandState('italic')) {
    state.activeFormats.push('italic');
  }
  if (document.queryCommandState('underline')) {
    state.activeFormats.push('underline');
  }
  // Check for lists
  if (document.queryCommandState('insertUnorderedList')) {
    state.activeFormats.push('insertUnorderedList');
  }
  if (document.queryCommandState('insertOrderedList')) {
    state.activeFormats.push('insertOrderedList');
  }
}

// Document command executor
const execCommand = (command: string, value?: string): void => {
  document.execCommand(command, false, value);
  updateInputField();
  // Check active formatting after command execution
  checkActiveFormats();
};

// Toolbar tools
const formattingTools = computed<FormattingTool[]>(() => [
  { icon: 'format_bold', command: 'bold', label: 'Bold', action: () => execCommand('bold') },
  {
    icon: 'format_italic',
    command: 'italic',
    label: 'Italic',
    action: () => execCommand('italic'),
  },
  {
    icon: 'format_underline',
    command: 'underline',
    label: 'Underline',
    action: () => execCommand('underline'),
  },
  { icon: 'link', command: 'createLink', label: 'Insert Link', action: insertLink },
  {
    icon: 'format_list_bulleted',
    command: 'insertUnorderedList',
    label: 'Bullet List',
    action: () => execCommand('insertUnorderedList'),
  },
  {
    icon: 'format_list_numbered',
    command: 'insertOrderedList',
    label: 'Numbered List',
    action: () => execCommand('insertOrderedList'),
  },
  {
    icon: 'format_clear',
    command: 'removeFormat',
    label: 'Clear Formatting',
    action: () => {
      execCommand('removeFormat');
      state.activeFormats = []; // Clear active formats when removing all formatting
    },
  },
]);

// Event handlers
function showToolbar(): void {
  state.isToolbarVisible = true;
  // Check active formats when toolbar is shown
  checkActiveFormats();
}

function handleFocus(event: FocusEvent): void {
  state.isFocused = true;
  emit('focus', event);
  // Check active formats on focus
  checkActiveFormats();
}

function handleBlur(event: FocusEvent): void {
  state.isFocused = false;
  emit('blur', event);
}

// Refs for link popup
const showLinkPopup = ref(false);
const linkUrl = ref('');
const linkText = ref('');
const urlInputRef = ref<HTMLElement | null>(null);

// Computed property to check if text is selected
const isTextSelected = computed((): boolean => {
  const selection = window.getSelection();
  return Boolean(selection && selection.toString().length > 0);
});

// Store the selection when inserting a link
let savedRange: Range | null = null;

function insertLink(): void {
  // Save current selection for later use
  const selection = window.getSelection();
  if (selection && selection.rangeCount > 0) {
    savedRange = selection.getRangeAt(0).cloneRange();

    // Get selected text to use as link text
    if (selection.toString()) {
      linkText.value = selection.toString();
    } else {
      linkText.value = '';
    }
  } else {
    savedRange = null;
    linkText.value = '';
  }

  // Reset URL input
  linkUrl.value = '';

  // Show popup dialog
  showLinkPopup.value = true;
}

function applyLink(): void {
  if (linkUrl.value.trim() === '') {
    return; // Don't proceed if URL is empty
  }

  // Close popup first to prevent blur issues
  showLinkPopup.value = false;

  // Format URL if needed (add https:// if not present)
  let url = linkUrl.value;
  if (!/^https?:\/\//i.test(url)) {
    url = 'https://' + url;
  }

  // Store values before resetting them
  const finalLinkText = linkText.value;

  // Reset values
  linkUrl.value = '';
  linkText.value = '';

  // Delay link insertion slightly to avoid race conditions with dialog closing
  setTimeout(() => {
    // Make sure we have focus back in the editor before inserting
    if (editableFieldRef.value) {
      editableFieldRef.value.focus();
    }

    try {
      // Restore saved selection if available
      if (savedRange) {
        const selection = window.getSelection();
        if (selection) {
          selection.removeAllRanges();
          selection.addRange(savedRange);
        }
      }

      // If link text is provided but no text is selected, insert new link
      if (finalLinkText && !window.getSelection()?.toString()) {
        document.execCommand(
          'insertHTML',
          false,
          `<a href="${url}" target="_blank">${finalLinkText}</a>`,
        );
      } else {
        // Apply link to selection
        document.execCommand('createLink', false, url);

        // Set target="_blank" to open links in a new tab
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const linkElement = range.startContainer.parentElement;
          if (linkElement && linkElement.tagName === 'A') {
            linkElement.setAttribute('target', '_blank');
          }
        }
      }

      // Clear saved range
      savedRange = null;

      // Update content
      updateInputField();
    } catch (error) {
      console.error('Error inserting link:', error);
    }
  }, 50); // Small delay to ensure dialog is fully closed
}

function updateInputField(): void {
  if (editableFieldRef.value) {
    const innerHTML = editableFieldRef.value.innerHTML;
    state.formattedContent = innerHTML;

    const textContent = editableFieldRef.value.innerText.trim();
    state.hasContent = textContent !== '';

    emit('update:content', innerHTML);
  }
}

function handleClickOutside(event: MouseEvent): void {
  const target = event.target as Node;
  const isClickInsideEditor = editableFieldRef.value?.contains(target);
  const isClickInsideToolbar = toolbarRef.value?.contains(target);

  if (!isClickInsideEditor && !isClickInsideToolbar) {
    state.isToolbarVisible = false;
  }
}

// Function to update editor content
function updateEditorContent(content: string) {
  if (editableFieldRef.value && content !== undefined) {
    editableFieldRef.value.innerHTML = content;
    state.formattedContent = content;
    const textContent = editableFieldRef.value.innerText.trim();
    state.hasContent = textContent !== '';
  }
}

// Watch for changes to initialValue prop
watch(
  () => props.initialValue,
  (newValue) => {
    if (newValue !== undefined && newValue !== state.formattedContent) {
      updateEditorContent(newValue);
    }
  },
  { immediate: false },
);

// Lifecycle hooks
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  document.addEventListener('selectionchange', checkActiveFormats);

  // Initialize with initial content if provided
  if (props.initialValue && editableFieldRef.value) {
    updateEditorContent(props.initialValue);
  }
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
  document.removeEventListener('selectionchange', checkActiveFormats);

  // Clean up
  savedRange = null;
  showLinkPopup.value = false;
});
</script>

<style scoped lang="scss">
.q-toolbar {
  width: 320px;
  background-color: transparent;
}

.q-separator {
  width: 100%;
  max-width: 100%;
}

.editable-div {
  width: 100%;
  min-height: 2rem;
  border-radius: $generic-border-radius;
  background: $surface-field;
  box-sizing: border-box;
  font-size: 16px;
}

.header-font {
  font-size: 30px !important;
}

.btn-tool {
  background: transparent;
}

.active-format {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Medium mobile screens (≥ 376px) */
@media (min-width: 376px) {
  .input-wrapper {
    min-width: 330px;
  }

  .editable-div {
    min-width: 250px;
    padding: 8px 16px;
  }
}

/* Small mobile screens (< 375px) */
@media (max-width: 375px) {
  .input-wrapper {
    max-width: 100%;
    padding: 0 5px;
  }

  .editable-div {
    min-width: 300px;
    font-size: 13px;
    padding: 8px 16px;
  }
}

.editable-div:empty:before {
  content: attr(data-placeholder);
  color: #aaa;
  pointer-events: none;
  display: block;
}

.toolbar {
  margin: 0;
  padding: 4px 8px;
  border-radius: 8px;
}

.slideTool {
  min-height: 10px;
  width: 100%;
}

// Link popup styling
:deep(.q-popup-edit) {
  min-width: 320px;

  .q-field {
    margin-bottom: 10px;
  }

  .text-h6 {
    margin-bottom: 16px;
    color: $primary;
    font-weight: 500;
  }

  .q-form {
    padding: 0 8px;
  }
}

// Link dialog styling
:deep(.q-dialog__inner) {
  .q-card {
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .text-h6 {
    font-weight: 500;
    color: $primary;
  }

  .q-field {
    margin-bottom: 16px;
  }
}
</style>
