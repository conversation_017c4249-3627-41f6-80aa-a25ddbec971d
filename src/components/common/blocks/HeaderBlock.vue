<template>
  <q-card @click="handleCardClick" class="q-mb-md header-block-container">
    <!-- Header with three-dot menu and save indicator -->
    <div class="header-top-bar">
      <div class="row items-center q-gutter-sm">
        <!-- Three-dot menu -->
        <q-btn flat round color="grey-6" size="sm" class="three-dot-menu" @click.stop="toggleMenu">
          <ThreeDots size="xs" color="grey-6" />
          <q-menu v-model="showMenu" anchor="bottom middle" self="top middle">
            <q-list style="min-width: 150px">
              <q-item clickable v-close-popup @click="onDuplicate">
                <q-item-section avatar>
                  <q-icon name="content_copy" />
                </q-item-section>
                <q-item-section>Duplicate</q-item-section>
              </q-item>
              <q-item clickable v-close-popup @click="onDelete">
                <q-item-section avatar>
                  <q-icon name="delete" />
                </q-item-section>
                <q-item-section>Delete</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </div>
    </div>

    <!-- Title Editor -->
    <div class="input-wrapper">
      <EditorTool
        ref="titleEditor"
        :label="'หัวข้อ...'"
        :isHeader="true"
        :initialValue="titleContent"
        @update:content="updateTitle"
        @focus="isTitleFocused = true"
        @blur="handleTitleBlur"
      />
    </div>

    <!-- Description Editor -->
    <div class="input-wrapper">
      <EditorTool
        ref="descriptionEditor"
        :label="'รายละเอียด...'"
        :initialValue="descriptionContent"
        @update:content="updateDescription"
        @focus="isDescriptionFocused = true"
        @blur="handleDescriptionBlur"
      />
      <ItemBlockFooter
        v-if="props.index > 0"
        label="ข้อความ"
        @duplicate="onFooterDuplicate"
        @delete="onFooterDelete"
      />
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import EditorTool from 'src/components/common/EditorTool.vue';
import ThreeDots from 'src/components/common/ThreeDots.vue';
import type { ItemBlock } from 'src/types/models';
import ItemBlockFooter from './ItemBlockFooter.vue';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useGlobalStore } from 'src/stores/global';

const props = defineProps<{
  itemBlock: ItemBlock;
  index: number;
  type?: 'quiz' | 'evaluate';
}>();

// Define emits
const emit = defineEmits([
  'blur',
  'focus-fab',
  'update:title',
  'update:description',
  'duplicate',
  'delete',
]);

// Initialize assessment service and global store
const assessmentService = new AssessmentService(props.type || 'evaluate');
const globalStore = useGlobalStore();

// Refs for editors
const titleEditor = ref(null);
const descriptionEditor = ref(null);

// Content refs to track values locally
const titleContent = ref<string>(props.itemBlock.headerBody?.title || '');
const descriptionContent = ref<string>(props.itemBlock.headerBody?.description || '');

// Focus state for styling
const isTitleFocused = ref(false);
const isDescriptionFocused = ref(false);

// Menu state
const showMenu = ref(false);

// Save state tracking
const lastSavedTitle = ref<string>(props.itemBlock.headerBody?.title || '');
const lastSavedDescription = ref<string>(props.itemBlock.headerBody?.description || '');
const isSaving = ref(false);

// Update functions for local content (real-time updates without saving)
function updateTitle(content: string) {
  titleContent.value = content;
  emit('update:title', content);
  // No auto-save here - save only on blur
}

function updateDescription(content: string) {
  descriptionContent.value = content;
  emit('update:description', content);
  // No auto-save here - save only on blur
}

// Perform save operation on blur
async function performSave(field: 'title' | 'description', content: string) {
  try {
    // Validate that we have the necessary IDs
    const headerBodyId = props.itemBlock.headerBody?.id;
    const itemBlockId = props.itemBlock.id;

    if (!headerBodyId || !itemBlockId) {
      return;
    }

    // Only save if content has changed
    const lastSavedValue = field === 'title' ? lastSavedTitle.value : lastSavedDescription.value;
    if (content.trim() === lastSavedValue) {
      return;
    }

    isSaving.value = true;
    globalStore.startSaveOperation('Saving...');

    // Prepare the update payload for /evaluate/header-bodies/{id} endpoint
    const updatePayload = {
      itemBlockId: itemBlockId, // Required itemBlockId in body
      [field]: content,
      // Include the other field to maintain data integrity
      [field === 'title' ? 'description' : 'title']:
        field === 'title' ? descriptionContent.value : titleContent.value,
    };

    // Call the updateHeaderBody API using /evaluate/header-bodies/{id} endpoint
    const updatedHeaderBody = await assessmentService.updateHeaderBody(headerBodyId, updatePayload);

    if (updatedHeaderBody) {
      // Update the last saved values
      if (field === 'title') {
        lastSavedTitle.value = content;
      } else {
        lastSavedDescription.value = content;
      }

      globalStore.completeSaveOperation(true, 'Saved successfully');

      // No need to refetch the entire assessment - the PATCH response contains the updated data
      // The local state is already updated and in sync with the server
    }
  } catch {
    // Save failed silently
    globalStore.completeSaveOperation(false, 'Saved successfully');
  } finally {
    isSaving.value = false;
  }
}

// Handlers for blur events that trigger save
async function handleTitleBlur() {
  isTitleFocused.value = false;

  // Emit blur event for parent component compatibility
  const content = titleContent.value;
  if (content && content.trim() !== '') {
    emit('blur', 'title', content);
  }

  // Trigger save on blur
  await performSave('title', content);
}

async function handleDescriptionBlur() {
  isDescriptionFocused.value = false;

  // Emit blur event for parent component compatibility
  const content = descriptionContent.value;
  if (content && content.trim() !== '') {
    emit('blur', 'description', content);
  }

  // Trigger save on blur
  await performSave('description', content);
}

// Watch for prop changes to update local state
watch(
  () => props.itemBlock.headerBody?.title,
  (newTitle) => {
    if (newTitle !== undefined && newTitle !== titleContent.value) {
      titleContent.value = newTitle;
      lastSavedTitle.value = newTitle;
    }
  },
);

watch(
  () => props.itemBlock.headerBody?.description,
  (newDescription) => {
    if (newDescription !== undefined && newDescription !== descriptionContent.value) {
      descriptionContent.value = newDescription;
      lastSavedDescription.value = newDescription;
    }
  },
);

// Menu handler functions
function toggleMenu() {
  showMenu.value = !showMenu.value;
}

function onDuplicate() {
  emit('duplicate');
}

function onDelete() {
  // Simplified validation - only check for required ID
  if (!props.itemBlock.id) {
    return;
  }

  emit('delete');
}

// Footer event handlers (same as menu handlers for consistency)
function onFooterDuplicate() {
  onDuplicate();
}

function onFooterDelete() {
  onDelete();
}

// Handle card click with debouncing to prevent conflicts
let cardClickTimeout: NodeJS.Timeout | null = null;
function handleCardClick(event: Event) {
  // Clear any existing timeout
  if (cardClickTimeout) {
    clearTimeout(cardClickTimeout);
  }

  // Debounce the focus-fab emission to prevent conflicts with rapid events
  cardClickTimeout = setTimeout(() => {
    // Only emit focus-fab if the click wasn't on a button or interactive element
    const target = event.target as HTMLElement;
    if (!target.closest('button') && !target.closest('.q-btn') && !target.closest('.q-menu')) {
      emit('focus-fab');
    }
  }, 100);
}
</script>

<style scoped>
.input-wrapper {
  position: relative;
  margin: 16px;
}

.header-block-container {
  position: relative;
}

.header-top-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 8px;
}

.three-dot-menu {
  background-color: transparent;
  border: none;
  border-radius: 0;
  padding: 4px;
  cursor: grab;
  outline: none;
}

.three-dot-menu:hover {
  background-color: transparent;
}

.three-dot-menu:active {
  cursor: grabbing;
}

.three-dot-menu:focus {
  outline: none;
  box-shadow: none;
}

.save-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(25, 118, 210, 0.1);
  border: 1px solid rgba(25, 118, 210, 0.2);
  border-radius: 8px;
  padding: 4px 8px;
  font-size: 0.75rem;
}

.save-text {
  color: #1976d2;
  font-weight: 500;
}
</style>
