<template>
  <q-card class="float-btn q-ml-md" :class="{ 'quiz-mode': props.type === 'quiz' }">
    <div>
      <q-btn
        icon="app:add-circle"
        round
        flat
        class="custom-icon-btn"
        :disable="disabled"
        :loading="disabled"
        @click="$emit('add')"
      >
      </q-btn>

      <q-btn
        icon="app:text"
        round
        flat
        class="custom-icon-btn"
        :disable="disabled"
        @click="$emit('add-text')"
      >
      </q-btn>

      <q-btn
        icon="app:image"
        round
        flat
        class="custom-icon-btn"
        :disable="disabled"
        :loading="isCreatingImageBlock"
        @click="handleAddImageClick"
      >
      </q-btn>

      <q-btn
        v-if="props.type !== 'quiz'"
        icon="app:section"
        round
        flat
        class="custom-icon-btn"
        :disable="disabled"
        @click="$emit('add-section')"
      >
      </q-btn>
    </div>

    <UploadImage
      v-model="showUploadDialog"
      :item-block-id="createdImageBlockId"
      @image-uploaded="handleImageUploaded"
    />
  </q-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import UploadImage from '../UploadImage.vue';

const props = defineProps<{
  disabled?: boolean;
  type?: 'quiz' | 'evaluate';
}>();

const emit = defineEmits(['add', 'add-text', 'add-section', 'add-image', 'image-uploaded']);
const showUploadDialog = ref(false);
const isCreatingImageBlock = ref(false);
const createdImageBlockId = ref<number | null>(null);

async function handleAddImageClick() {
  try {
    isCreatingImageBlock.value = true;

    // Step 1: Create the IMAGE ItemBlock first
    const itemBlockId = await new Promise<number>((resolve, reject) => {
      // Emit event to parent to create the block and get the ID back
      emit('add-image', {
        callback: (id: number | null) => {
          if (id) {
            resolve(id);
          } else {
            reject(new Error('Failed to create image block'));
          }
        },
      });
    });

    // Step 2: Store the created block ID and open upload dialog
    createdImageBlockId.value = itemBlockId;
    showUploadDialog.value = true;
  } catch (error) {
    console.error('❌ Failed to create image block:', error);
  } finally {
    isCreatingImageBlock.value = false;
  }
}

function handleImageUploaded() {
  // Close dialog and reset state
  showUploadDialog.value = false;
  createdImageBlockId.value = null;

  // Emit to parent that image was uploaded successfully
  emit('image-uploaded');
}
</script>

<style scoped>
.float-btn {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding: 12px;
  height: 190px;
  width: 48px;
}

.float-btn.quiz-mode {
  height: 150px; /* Reduced height for quiz mode (3 buttons instead of 4) */
}
</style>
