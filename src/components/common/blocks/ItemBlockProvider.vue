<script setup lang="ts">
import { provide, watch } from 'vue';
import { createItemBlockStore } from 'src/stores/item_block_store';
import type { ItemBlock } from 'src/types/models';

const props = defineProps<{
  blockId: number;
  itemBlock?: ItemBlock;
}>();

// Create the store for this block, passing the itemBlock data
const blockStore = createItemBlockStore(props.blockId, props.itemBlock);
const storeInstance = blockStore();

// Watch for changes in itemBlock prop and re-initialize store when it changes
watch(
  () => props.itemBlock,
  (newItemBlock, oldItemBlock) => {
    if (newItemBlock && oldItemBlock && newItemBlock.id === oldItemBlock.id) {
      // Only re-initialize if the itemBlock data has actually changed
      // Check for type changes or options changes
      const typeChanged = newItemBlock.type !== oldItemBlock.type;
      const optionsChanged =
        (newItemBlock.options?.length || 0) !== (oldItemBlock.options?.length || 0) ||
        JSON.stringify(newItemBlock.options) !== JSON.stringify(oldItemBlock.options);

      if (typeChanged || optionsChanged) {
        console.log('🔄 [PROVIDER] ItemBlock data changed, re-initializing store:', {
          blockId: newItemBlock.id,
          typeChanged,
          optionsChanged,
          oldType: oldItemBlock.type,
          newType: newItemBlock.type,
          oldOptionsCount: oldItemBlock.options?.length || 0,
          newOptionsCount: newItemBlock.options?.length || 0,
        });

        storeInstance.reinitializeWithUpdatedData(newItemBlock);
      }
    }
  },
  { deep: true }
);

// Provide the store to child components
provide('blockStore', storeInstance);
</script>

<template>
  <slot />
</template>
