<template>
  <q-dialog v-model="dialog" persistent>
    <q-card class="q-pa-lg q-mx-md" style="border-radius: 16px; width: 350px; max-width: 90vw">
      <!-- QR Code -->
      <div class="flex justify-center q-mb-lg">
        <div class="flex flex-center" style="width: 160px; height: 160px">
          <q-img :src="qrImage" style="width: 140px; height: 140px" fit="contain" />
        </div>
      </div>

      <!-- ลิงก์ -->
      <q-input
        dense
        borderless
        readonly
        v-model="link"
        class="q-px-sm"
        style="border-radius: 6px; background-color: #bdbdbd"
      >
        <template v-slot:append>
          <q-icon name="content_copy" class="cursor-pointer" @click="copyLink" />
        </template>
      </q-input>

      <!-- ปุ่ม -->
      <div class="row justify-between q-mt-lg">
        <q-btn
          unelevated
          label="บันทึก QR Code"
          color="secondary"
          class="col-5"
          @click="saveQR"
          style="width: 130px"
        />
        <q-btn
          unelevated
          label="ยืนยัน"
          color="positive"
          class="col-5"
          @click="confirm"
          style="width: 130px"
        />
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { copyToClipboard } from 'quasar';

const dialog = ref(false);
const link = ref('');
const confirmLabel = ref('ยืนยัน');
const qrImage = ref('');

function openDialog(linkStr: string, okStr: string) {
  console.log(linkStr);

  link.value = `http://localhost:9000/evaluate/` + linkStr;
  confirmLabel.value = okStr;
  qrImage.value =
    'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=' +
    encodeURIComponent(link.value);
  dialog.value = true;
}

async function copyLink() {
  await copyToClipboard(link.value).then(() => {
    console.log('คัดลอกลิงก์แล้ว');
  });
}

function saveQR() {
  const a = document.createElement('a');
  a.href = qrImage.value;
  a.download = 'qr-code.png';
  a.click();
}

function confirm() {
  dialog.value = false;
  console.log('ยืนยันแล้ว');
}

defineExpose({ openDialog });
</script>
