<template>
  <div class="chart-wrapper">
    <canvas ref="canvas"></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, defineExpose } from 'vue';
import {
  Chart,
  BarController,
  BarElement,
  CategoryScale,
  LinearScale,
  Tooltip,
  Legend,
} from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';

Chart.register(
  BarController,
  BarElement,
  CategoryScale,
  LinearScale,
  Tooltip,
  Legend,
  ChartDataLabels
);

interface Dataset {
  label: string;
  values: number[];
}

const props = defineProps<{
  labels: string[];
  data: Dataset[];
}>();

const canvas = ref<HTMLCanvasElement | null>(null);
let chartInstance: Chart | null = null;

function generateColors(count: number): string[] {
  const baseColors = ['#3d3c91', '#6a4cb3', '#a65fd3', '#d173e7', '#ff87ff'];
  return Array.from({ length: count }, (_, i) => baseColors[i % baseColors.length]!);
}

function transformChartData(data: { labels: string[]; datasets: Dataset[] }) {
  if (!data.labels || data.labels.length !== 1) return data;

  if (!data.datasets || data.datasets.length === 0) return { labels: [], datasets: [] };

  const valuesMatrix = data.datasets.map((d) => d.values ?? []);
  if (valuesMatrix.length === 0 || valuesMatrix[0]!.length === 0)
    return { labels: [], datasets: [] };

  const transposed = valuesMatrix[0]!.map((_, colIndex) =>
    valuesMatrix.map((row) => row[colIndex] ?? 0),
  );

  const newDatasets: Dataset[] = transposed.map((vals, idx) => ({
    label: data.labels[idx] ?? '',
    values: vals,
  }));

  return {
    labels: data.datasets.map((d) => d.label ?? ''),
    datasets: newDatasets,
  };
}

function renderChart() {
  if (!canvas.value) return;
  if (chartInstance) chartInstance.destroy();

  const colors = generateColors(props.data.length);
  const isSingleLabel = props.labels.length === 1;

  const chartData = isSingleLabel
    ? transformChartData({ labels: props.labels, datasets: props.data })
    : { labels: props.labels, datasets: props.data };
  const datasets = chartData.datasets.map((ds, idx) => ({
    label: ds.label,
    data: ds.values,
    backgroundColor: colors[idx],
  }));

  // คำนวณค่าสูงสุดของข้อมูล
  const maxValue = Math.max(
    ...chartData.datasets.flatMap((ds) => ds.values),
    0
  );

  // ตั้งค่า max ของแกน Y ให้สูงกว่าค่าสูงสุด 10%
  const yMax = maxValue > 0 ? maxValue * 1.1 : 10;

  // ตรวจสอบว่าเป็น multiple bar หรือไม่
  const isMultipleBar = chartData.labels.length > 1 && chartData.datasets.length > 1;

  chartInstance = new Chart(canvas.value, {
    type: 'bar',
    data: {
      labels: chartData.labels,
      datasets,
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          display: true,
        },
        y: {
          beginAtZero: true,
          max: yMax,
          ticks: {
            stepSize: Math.ceil(yMax / 10), // ปรับให้ถี่ขึ้นโดยหารด้วย 10 (จากเดิม 5)
          },
        },
      },
      plugins: {
        legend: {
          display: props.labels.length > 1,
        },
        datalabels: {
          display: !isMultipleBar,
          color: 'black',
          anchor: 'end',
          align: 'top',
          formatter: (value: number, context) => {
            const dataset = chartData.datasets[context.datasetIndex];
            const totalValue = dataset!.values.reduce((a, b) => a + b, 0);
            const percentage = totalValue > 0 ? ((value / totalValue) * 100).toFixed(1) : '0.0';
            return `${value} (${percentage}%)`;
          },
          font: {
            weight: 'bold',
          },
        },
      },
    },
  });
}

onMounted(renderChart);

watch(
  () => [props.labels, props.data],
  () => {
    renderChart();
  },
  { deep: true },
);

onBeforeUnmount(() => {
  if (chartInstance) chartInstance.destroy();
});

defineExpose({ canvas });
</script>

<style scoped>
canvas {
  width: 100% !important;
  height: 300px !important;
}
</style>