<template>
  <q-page class="animated-page">
    <div class="container">
      <!-- Header with Floating Effect -->
      <div class="header-section slide-in-top">
        <q-btn
          flat
          round
          dense
          icon="arrow_back"
          @click="goBack"
          class="back-btn pulse-hover"
          size="lg"
        >
          <q-tooltip>กลับไปหน้าก่อน</q-tooltip>
        </q-btn>
        <div class="header-content">
          <div class="header-title">
            <q-icon name="assignment" class="q-mr-sm" /> รายละเอียดผู้เข้าสอบ
          </div>
          <div class="header-subtitle">ข้อมูลและผลการทดสอบ</div>
        </div>
      </div>

      <!-- Loading State with Cute Animation -->
      <div v-if="dashboardStore.isLoadingParticipantDetails" class="loading-container fade-in">
        <div class="loading-content">
          <div class="cute-loader">
            <q-spinner-dots size="3em" color="primary" />
          </div>
          <div class="loading-text bounce-in">
            <span>กำลังโหลดข้อมูล</span>
            <div class="loading-dots"><span>.</span><span>.</span><span>.</span></div>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <transition name="slide-fade">
        <q-banner
          v-if="dashboardStore.error && !dashboardStore.isLoadingParticipantDetails"
          class="error-banner"
        >
          <template v-slot:avatar>
            <q-icon name="error" class="error-icon shake" />
          </template>
          <div class="error-content">
            <div class="error-title">เกิดข้อผิดพลาด!</div>
            <div class="error-message">{{ dashboardStore.error }}</div>
          </div>
          <template v-slot:action>
            <q-btn flat class="retry-btn pulse-hover" label="ลองใหม่" @click="retryFetch">
              <q-icon name="refresh" class="q-ml-sm" />
            </q-btn>
            <q-btn flat class="clear-btn" label="ล้าง" @click="dashboardStore.clearError">
              <q-icon name="clear" class="q-ml-sm" />
            </q-btn>
          </template>
        </q-banner>
      </transition>

      <!-- Participant Details Content -->
      <div
        v-if="dashboardStore.participantDetails && !dashboardStore.isLoadingParticipantDetails"
        class="content-container"
      >
        <!-- Participant Summary Card -->
        <div class="summary-card slide-in-left" :style="{ animationDelay: '0.1s' }">
          <div class="card-header">
            <q-icon name="person" class="card-icon" />
            <div class="card-title">ข้อมูลสรุป</div>
          </div>

          <div class="summary-grid">
            <div class="summary-item hover-lift" :style="{ animationDelay: '0.2s' }">
              <q-icon name="badge" class="item-icon" />
              <div class="item-content">
                <div class="item-label">ชื่อผู้เข้าสอบ</div>
                <div class="item-value">{{ dashboardStore.participantDetails.userName }}</div>
              </div>
            </div>

            <div class="summary-item hover-lift" :style="{ animationDelay: '0.3s' }">
              <q-icon name="grade" class="item-icon" />
              <div class="item-content">
                <div class="item-label">คะแนนรวม</div>
                <div class="item-value score-highlight">
                  {{ dashboardStore.participantDetails.totalScore }}/{{
                    dashboardStore.participantDetails.maxScore
                  }}
                  <span class="percentage"
                    >({{
                      Number(dashboardStore.participantDetails.scorePercentage).toFixed(2)
                    }}%)</span
                  >
                </div>
              </div>
            </div>

            <div class="summary-item hover-lift" :style="{ animationDelay: '0.4s' }">
              <q-icon name="schedule" class="item-icon" />
              <div class="item-content">
                <div class="item-label">เวลาเริ่ม</div>
                <div class="item-value">
                  {{ formatDateTime(dashboardStore.participantDetails.startTime) }}
                </div>
              </div>
            </div>

            <div class="summary-item hover-lift" :style="{ animationDelay: '0.5s' }">
              <q-icon name="done_all" class="item-icon" />
              <div class="item-content">
                <div class="item-label">เวลาสิ้นสุด</div>
                <div class="item-value">
                  {{ formatDateTime(dashboardStore.participantDetails.endTime) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Questions and Answers -->
        <div class="questions-section slide-in-right" :style="{ animationDelay: '0.6s' }">
          <div class="section-header">
            <q-icon name="description" class="section-icon" />
            <div class="section-title">รายละเอียดคำตอบ</div>
          </div>

          <div class="questions-container">
            <div
              v-for="(question, index) in paginatedQuestions"
              :key="question.questionId"
              class="question-card hover-scale"
              :style="{ animationDelay: `${0.7 + index * 0.1}s` }"
            >
              <div class="question-header">
                <div class="question-row">
                  <div class="question-number">{{ question.questionSequence }}</div>
                  <div class="question-text">{{ question.questionText }}</div>
                </div>
                <div class="question-status">
                  <div class="score-badge">
                    <q-icon name="star" class="score-icon" />
                    <span class="score-value">{{ question.score }} คะแนน</span>
                  </div>
                  <div class="status-badge" :class="question.isCorrect ? 'correct' : 'incorrect'">
                    <q-icon
                      :name="question.isCorrect ? 'check_circle' : 'cancel'"
                      class="status-icon"
                    />
                    <span class="status-text">{{ question.isCorrect ? 'ถูกต้อง' : 'ผิด' }}</span>
                  </div>
                </div>
              </div>

              <div v-if="question.selectedOptionText" class="selected-answer">
                <div class="answer-label">
                  <q-icon name="check_circle_outline" class="q-mr-xs" /> คำตอบที่เลือก:
                </div>
                <div class="answer-value">{{ question.selectedOptionText }}</div>
              </div>

              <div class="all-options">
                <div class="options-label">
                  <q-icon name="list" class="q-mr-xs" /> ตัวเลือกทั้งหมด:
                </div>
                <div class="options-list">
                  <div
                    v-for="option in question.options"
                    :key="option.id"
                    class="option-item"
                    :class="{ selected: option.isSelected }"
                  >
                    <div class="option-radio">
                      <div class="radio-outer">
                        <div v-if="option.isSelected" class="radio-inner"></div>
                      </div>
                    </div>
                    <div class="option-text">{{ option.text }}</div>
                    <div v-if="option.isSelected" class="selected-tag">
                      <q-icon name="done" class="q-mr-xs" /> เลือก
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Pagination Component -->
            <div class="pagination-container">
              <q-pagination
                v-model="currentPage"
                :max="totalPages"
                :max-pages="6"
                :boundary-links="true"
                :direction-links="true"
                active-color="amber"
                active-text-color="dark"
                class="custom-pagination"
                @update:model-value="handlePageChange"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- No Data State -->
      <div
        v-else-if="!dashboardStore.isLoadingParticipantDetails && !dashboardStore.error"
        class="no-data-container fade-in"
      >
        <div class="no-data-content">
          <q-icon name="inbox" class="no-data-icon bounce" />
          <div class="no-data-title">ไม่พบข้อมูล</div>
          <div class="no-data-subtitle">ไม่พบข้อมูลผู้เข้าสอบที่คุณต้องการ</div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuizDashboardStore } from 'src/stores/quizdashboardStore';
import type { DataParams } from 'src/types/data';

const route = useRoute();
const router = useRouter();
const dashboardStore = useQuizDashboardStore();

// Initialize pagination parameters with type safety
const paginationParams = ref<DataParams>({
  page: 1,
  limit: 5,
  sortBy: 'questionSequence',
  order: 'ASC',
  search: null,
});

// Update currentPage and itemsPerPage to use paginationParams
const currentPage = computed({
  get: () => paginationParams.value.page,
  set: (value) => {
    paginationParams.value.page = value;
  },
});

const itemsPerPage = computed({
  get: () => paginationParams.value.limit,
  set: (value) => {
    paginationParams.value.limit = value;
  },
});

const participantId = Number(route.params.participantId);

// Pagination state with type safety
// const currentPage = ref<number>(1);
// const itemsPerPage = ref<number>(5);

interface Question {
  questionId: number;
  questionSequence: number;
  questionText: string;
  score: number;
  isCorrect: boolean;
  selectedOptionText?: string;
  options: Array<{
    id: number;
    text: string;
    isSelected: boolean;
  }>;
}

// Compute total pages based on questions length
const totalPages = computed((): number => {
  if (!dashboardStore.participantDetails?.questions) return 1;
  return Math.ceil(dashboardStore.participantDetails.questions.length / itemsPerPage.value);
});

// Get paginated questions with proper typing
const paginatedQuestions = computed((): Question[] => {
  if (!dashboardStore.participantDetails?.questions) return [];

  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;

  return dashboardStore.participantDetails.questions.slice(start, end);
});

// Methods
function formatDateTime(dateString: string): string {
  if (!dateString) return 'N/A';
  try {
    return new Date(dateString).toLocaleString('th-TH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch (e) {
    console.error('[ParticipantDetailsPage] Error formatting date:', e);
    return dateString;
  }
}

function goBack() {
  router.back();
}

async function retryFetch() {
  dashboardStore.clearError();
  await fetchParticipantDetails();
}

async function fetchParticipantDetails() {
  if (participantId && !isNaN(participantId)) {
    try {
      await dashboardStore.fetchParticipantDetails(participantId);
    } catch (error) {
      console.error('Failed to fetch participant details:', error);
    }
  }
}

async function handlePageChange(newPage: number) {
  paginationParams.value.page = newPage;
  window.scrollTo({ top: 0, behavior: 'smooth' });
  await fetchParticipantDetails();
}

// Lifecycle
onMounted(async () => {
  console.log('[ParticipantDetailsPage] Component mounted. Participant ID:', participantId);
  // Scroll to top when component mounts
  window.scrollTo({ top: 0, behavior: 'instant' });
  await fetchParticipantDetails();
});
</script>

<style scoped>
/* Base Page Styles */
.animated-page {
  background: linear-gradient(
    135deg,
    #474747 0%,
    #535353 25%,
    #666666 50%,
    #787878 75%,
    #8a8a8a 100%
  );
  min-height: 100vh;
  padding: 0;
  position: relative;
  overflow-x: hidden;
}

.animated-page::before {
  content: '';
  position: fixed;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 30% 40%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 60%, rgba(184, 134, 11, 0.08) 0%, transparent 50%);
  animation: backgroundShift 20s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
  z-index: 1;
}

/* Header Styles */
@keyframes shimmerAnimation {
  0% {
    transform: translateX(-150%);
    opacity: 0;
  }
  15% {
    opacity: 1;
  }
  85% {
    opacity: 1;
  }
  100% {
    transform: translateX(150%);
    opacity: 0;
  }
}

.header-section {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  background: linear-gradient(135deg, rgba(71, 71, 71, 0.95) 0%, rgba(58, 58, 58, 0.9) 100%);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  padding: 25px 35px;
  border: 2px solid rgba(255, 215, 0, 0.3);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 0 0 1px rgba(255, 215, 0, 0.1);
  position: relative;
  overflow: hidden;
  opacity: 0;
  animation: fadeInScale 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.header-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    transparent 25%,
    rgba(255, 215, 0, 0.1) 35%,
    rgba(255, 215, 0, 0.2) 50%,
    rgba(255, 215, 0, 0.1) 65%,
    transparent 75%,
    transparent 100%
  );
  animation: shimmerAnimation 5s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

.back-btn {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.3) 0%, rgba(255, 215, 0, 0.2) 100%);
  color: #ffffff;
  margin-right: 25px;
  border-radius: 18px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid #ffd700;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.back-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(212, 175, 55, 0.3) 0%, transparent 70%);
  transition: all 0.4s ease;
  transform: translate(-50%, -50%);
}

.back-btn:hover::before {
  width: 200px;
  height: 200px;
}

.back-btn:hover {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.3) 0%, rgba(184, 134, 11, 0.25) 100%);
  transform: translateX(-8px) scale(1.05);
  box-shadow: 0 10px 25px rgba(184, 134, 11, 0.2);
}

.header-content {
  position: relative;
  z-index: 1;
}

.header-title {
  font-size: 2.4rem;
  font-weight: 800;
  color: #ffffff;
  margin-bottom: 8px;
  text-shadow:
    2px 2px 4px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(255, 215, 0, 0.5);
}

.header-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 215, 0, 0.8);
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* Loading Styles */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content {
  text-align: center;
  background: linear-gradient(135deg, rgba(71, 71, 71, 0.98) 0%, rgba(58, 58, 58, 0.95) 100%);
  padding: 50px;
  border-radius: 30px;
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 215, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.loading-content::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #d4af37, #b8860b, #daa520, #cd853f);
  border-radius: 32px;
  z-index: -1;
  animation: borderRotate 3s linear infinite;
  opacity: 0.6;
}

.cute-loader {
  margin-bottom: 25px;
}

.loading-text {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #8b7355 0%, #d4af37 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.loading-dots {
  display: flex;
  gap: 6px;
}

.loading-dots span {
  animation: dotBounce 1.4s ease-in-out infinite both;
  color: #d4af37;
  font-size: 1.5rem;
  font-weight: bold;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}
.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

/* Error Banner */
.error-banner {
  background: linear-gradient(135deg, #dc6545 0%, #c5523a 100%);
  color: white;
  border-radius: 25px;
  padding: 25px 35px;
  margin-bottom: 30px;
  border: 2px solid rgba(220, 101, 69, 0.3);
  box-shadow:
    0 15px 35px rgba(220, 101, 69, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.error-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 2s ease-in-out infinite;
}

/* Content Container */
.content-container {
  display: flex;
  flex-direction: column;
  gap: 35px;
}

/* Summary Card */
.summary-card {
  background: linear-gradient(135deg, rgba(71, 71, 71, 0.98) 0%, rgba(58, 58, 58, 0.95) 100%);
  border-radius: 30px;
  padding: 35px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 0 20px rgba(255, 215, 0, 0.2);
  border: 2px solid rgba(255, 215, 0, 0.4);
  position: relative;
  overflow: hidden;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #d4af37 0%, #b8860b 50%, #daa520 100%);
  animation: progressGlow 3s ease-in-out infinite;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(212, 175, 55, 0.15);
  position: relative;
}

.card-icon {
  font-size: 2.2rem;
  margin-right: 18px;
  color: #d4af37;
  animation: iconPulse 2s ease-in-out infinite;
}

.card-title {
  font-size: 2rem;
  font-weight: 800;
  color: #ffffff;
  text-shadow:
    2px 2px 4px rgba(0, 0, 0, 0.3),
    0 0 10px rgba(255, 215, 0, 0.4);
  position: relative;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

/* Animation for summary items */
.summary-item {
  background: linear-gradient(135deg, rgba(71, 71, 71, 0.9) 0%, rgba(58, 58, 58, 0.85) 100%);
  padding: 25px 30px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 215, 0, 0.3);
  position: relative;
  overflow: hidden;
  opacity: 0;
  animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.summary-item:nth-child(1) {
  animation-delay: 0.3s;
}
.summary-item:nth-child(2) {
  animation-delay: 0.5s;
}
.summary-item:nth-child(3) {
  animation-delay: 0.7s;
}
.summary-item:nth-child(4) {
  animation-delay: 0.9s;
}

.summary-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  transition: left 0.5s ease;
}

.summary-item:hover::before {
  left: 100%;
}

.summary-item:nth-child(1) {
  border-left: 5px solid #ffd700;
}
.summary-item:nth-child(2) {
  border-left: 5px solid #ffd700;
}
.summary-item:nth-child(3) {
  border-left: 5px solid #ffd700;
}
.summary-item:nth-child(4) {
  border-left: 5px solid #ffd700;
}

.summary-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 215, 0, 0.1);
  border-color: #ffd700;
  background: linear-gradient(135deg, rgba(71, 71, 71, 0.95) 0%, rgba(58, 58, 58, 0.9) 100%);
}

.item-icon {
  font-size: 2.8rem;
  margin-right: 25px;
  min-width: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffd700;
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));
}

.item-content {
  flex: 1;
}

.item-label {
  font-size: 1.2rem;
  color: rgba(255, 215, 0, 0.9);
  margin-bottom: 12px;
  font-weight: 600;
  letter-spacing: 0.5px;
  line-height: 1.5;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.item-value {
  font-size: 1.6rem;
  font-weight: 800;
  line-height: 1.4;
  letter-spacing: 0.5px;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.score-highlight {
  font-size: 1.8rem;
  font-weight: 800;
  line-height: 1.4;
  letter-spacing: 0.5px;
  background: linear-gradient(135deg, #ffd700 0%, #ffc000 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
}

.percentage {
  font-size: 1.4rem;
  color: rgba(255, 215, 0, 0.9);
  font-weight: 700;
  letter-spacing: 0.5px;
  margin-left: 8px;
}

/* Questions Section */
.questions-section {
  background: linear-gradient(135deg, rgba(71, 71, 71, 0.98) 0%, rgba(58, 58, 58, 0.95) 100%);
  border-radius: 30px;
  padding: 35px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 215, 0, 0.3);
  position: relative;
  overflow: hidden;
  opacity: 0;
  animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  animation-delay: 1s;
}

.questions-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #daa520 0%, #d4af37 50%, #b8860b 100%);
  animation: progressGlow 4s ease-in-out infinite reverse;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(212, 175, 55, 0.15);
}

.section-icon {
  font-size: 2.2rem;
  margin-right: 18px;
  color: #d4af37;
  animation: iconPulse 2.5s ease-in-out infinite;
}

.section-title {
  font-size: 2rem;
  font-weight: 800;
  color: #ffffff;
  text-shadow:
    2px 2px 4px rgba(0, 0, 0, 0.3),
    0 0 10px rgba(255, 215, 0, 0.4);
  position: relative;
}
.question-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.questions-container {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Question Card */
.question-card {
  background: linear-gradient(135deg, rgba(71, 71, 71, 0.95) 0%, rgba(58, 58, 58, 0.9) 100%);
  border-radius: 25px;
  padding: 30px;
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 215, 0, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  opacity: 0;
  animation: slideInRight 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.question-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.05), transparent);
  transition: left 0.6s ease;
}

.question-card:hover::before {
  left: 100%;
}

.question-card:hover {
  transform: scale(1.02);
  box-shadow:
    0 20px 45px rgba(139, 115, 85, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.95);
  border-color: rgba(212, 175, 55, 0.2);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(212, 175, 55, 0.15);
}

.question-number {
  background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
  color: white;
  width: 45px;
  height: 45px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 800;
  font-size: 1.2rem;
  flex-shrink: 0;
  box-shadow:
    0 8px 20px rgba(212, 175, 55, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  animation: numberPulse 3s ease-in-out infinite;
}

.question-text {
  flex: 1;
  font-size: 1.3rem;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.5;
}

.question-status {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  align-items: flex-end;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 18px;
  border-radius: 30px;
  font-weight: 700;
  font-size: 0.95rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.status-badge:hover {
  transform: scale(1.05);
}

.status-badge.correct {
  background: linear-gradient(135deg, #6ba644 0%, #5a8f37 100%);
  color: white;
  box-shadow: 0 8px 20px rgba(107, 166, 68, 0.3);
}

.status-badge.incorrect {
  background: linear-gradient(135deg, #dc6545 0%, #c5523a 100%);
  color: white;
  box-shadow: 0 8px 20px rgba(220, 101, 69, 0.3);
}

.score-badge {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 18px;
  background: linear-gradient(135deg, #daa520 0%, #cd853f 100%);
  color: white;
  border-radius: 25px;
  font-weight: 700;
  font-size: 0.9rem;
  box-shadow: 0 6px 15px rgba(218, 165, 32, 0.3);
  animation: scorePulse 2s ease-in-out infinite;
}
.score-badge:hover {
  transform: scale(1.05);
}
/* Selected Answer */
.selected-answer {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.15) 0%, rgba(255, 215, 0, 0.1) 100%);
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 25px;
  border-left: 5px solid #ffd700;
  position: relative;
  overflow: hidden;
}

.selected-answer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: linear-gradient(180deg, #d4af37 0%, #b8860b 100%);
  animation: borderGlow 2s ease-in-out infinite;
}

.answer-label {
  font-weight: 800;
  color: #ffd700;
  margin-bottom: 10px;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
}

.answer-value {
  font-size: 1.2rem;
  color: #ffffff;
  font-weight: 600;
  line-height: 1.4;
}

/* All Options */
.all-options {
  background: rgba(71, 71, 71, 0.8);
  border-radius: 20px;
  padding: 25px;
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.options-label {
  font-weight: 800;
  color: #ffd700;
  margin-bottom: 18px;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border-radius: 15px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  background: rgba(71, 71, 71, 0.5);
}

.option-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
  transition: left 0.5s ease;
}

.option-item:hover::before {
  left: 100%;
}

.option-item:hover {
  background: rgba(212, 175, 55, 0.05);
  transform: translateX(8px);
  box-shadow: 0 5px 15px rgba(212, 175, 55, 0.1);
}

.option-item.selected {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 215, 0, 0.1) 100%);
  border: 2px solid #ffd700;
  font-weight: 700;
  color: #ffd700;
  box-shadow:
    0 8px 20px rgba(255, 215, 0, 0.15),
    inset 0 1px 0 rgba(255, 215, 0, 0.1);
}

.option-radio {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.radio-outer {
  width: 24px;
  height: 24px;
  border: 3px solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.radio-outer::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(from 0deg, #d4af37, #b8860b, #daa520, #d4af37);
  opacity: 0;
  transition: opacity 0.4s ease;
  animation: radioSpin 2s linear infinite;
}

.radio-inner {
  width: 14px;
  height: 14px;
  background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
  border-radius: 50%;
  transform: scale(0);
  transition: transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  box-shadow: 0 2px 8px rgba(212, 175, 55, 0.4);
}

.option-item.selected .radio-outer {
  border-color: #d4af37;
}

.option-item.selected .radio-outer::before {
  opacity: 0.3;
}

.option-item.selected .radio-inner {
  transform: scale(1);
}

.option-text {
  flex: 1;
  font-size: 1.15rem;
  transition: all 0.3s ease;
  color: #ffffff;
}

.selected-tag {
  position: absolute;
  right: -60px;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
  color: white;
  padding: 8px 18px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 700;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
}

.option-item.selected .selected-tag {
  right: 15px;
  opacity: 1;
  animation: tagBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.option-item.selected .option-text {
  padding-right: 90px;
}

/* No Data State */
.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.no-data-content {
  text-align: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 245, 238, 0.9) 100%);
  padding: 50px;
  border-radius: 30px;
  box-shadow:
    0 25px 50px rgba(139, 115, 85, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(212, 175, 55, 0.15);
}

.no-data-icon {
  font-size: 4rem;
  color: #d4af37;
  margin-bottom: 20px;
  animation: noDataFloat 3s ease-in-out infinite;
}

.no-data-title {
  font-size: 1.8rem;
  font-weight: 800;
  background: linear-gradient(135deg, #8b7355 0%, #d4af37 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 10px;
}

.no-data-subtitle {
  font-size: 1.1rem;
  color: rgba(139, 115, 85, 0.8);
  font-weight: 500;
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  padding: 20px 0;
  position: relative;
}

.pagination-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 500px;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
}

.custom-pagination {
  position: relative;
  z-index: 1;
  padding: 8px;
  border-radius: 16px;
  background: linear-gradient(135deg, rgba(71, 71, 71, 0.95) 0%, rgba(58, 58, 58, 0.9) 100%);
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 215, 0, 0.2);
}

:deep(.custom-pagination .q-btn) {
  min-width: 40px;
  height: 40px;
  margin: 0 4px;
  border-radius: 12px;
  color: #ffd700;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;

  &:hover:not(.q-btn--active) {
    background: rgba(255, 215, 0, 0.1);
    border-color: rgba(255, 215, 0, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.2);
  }

  &.q-btn--active {
    background: linear-gradient(135deg, #ffd700 0%, #daa520 100%);
    color: #474747;
    font-weight: 700;
    transform: scale(1.1);
    box-shadow:
      0 6px 15px rgba(255, 215, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.5);
  }

  &[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
    &:hover {
      transform: none;
      box-shadow: none;
    }
  }
}

/* Icon buttons (first, prev, next, last) */
:deep(.custom-pagination .q-btn--flat) {
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(71, 71, 71, 0.8) 0%, rgba(58, 58, 58, 0.7) 100%);
  border: 2px solid rgba(255, 215, 0, 0.2);

  .q-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
  }

  &:hover:not([disabled]) {
    background: rgba(255, 215, 0, 0.1);
    .q-icon {
      transform: scale(1.2);
    }
  }
}

/* Page transition animations */
.fade-page-enter-active,
.fade-page-leave-active {
  transition:
    opacity 0.3s,
    transform 0.3s;
}

.fade-page-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.fade-page-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* Transition Animations */
.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

/* Custom Button Effects */
.pulse-hover {
  position: relative;
  overflow: hidden;
}

.pulse-hover::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(212, 175, 55, 0.4) 0%, transparent 70%);
  transition:
    width 0.4s ease,
    height 0.4s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  z-index: 0;
}

.pulse-hover:hover::after {
  width: 200px;
  height: 200px;
}

/* Entrance Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.fade-in-up {
  animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.fade-in-scale {
  animation: fadeInScale 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.slide-in-right {
  animation: slideInRight 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Responsive Design Enhancements */
@media (max-width: 1024px) {
  .header-title {
    font-size: 2.4rem;
  }

  .summary-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .header-title {
    font-size: 2rem;
  }

  .header-section {
    padding: 20px 25px;
  }

  .summary-item {
    padding: 20px;
  }

  .question-header {
    flex-direction: column;
  }

  .question-status {
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
  }
}

@media (max-width: 480px) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .back-btn {
    margin-bottom: 15px;
  }

  .question-card {
    padding: 20px;
  }

  .selected-tag {
    position: static;
    margin-top: 10px;
    transform: none;
  }

  .option-item.selected .option-text {
    padding-right: 0;
  }

  .loading-text {
    font-size: 1.2rem;
  }
}

/* Print Styles */
@media print {
  .animated-page {
    background: white !important;
    min-height: auto !important;
  }

  .container {
    padding: 0 !important;
  }

  .back-btn,
  .header-section::before,
  .summary-item::before,
  .question-card::before {
    display: none !important;
  }

  .summary-card,
  .questions-section,
  .question-card {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
  }

  .header-title,
  .card-title,
  .section-title {
    color: #000 !important;
    -webkit-text-fill-color: initial !important;
    text-shadow: none !important;
  }
}
</style>
