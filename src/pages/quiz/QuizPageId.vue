<template>
  <q-page class="q-pa-md">
    <div class="q-pa-md flex flex-center" style="margin-top: 1%">
      <div style="width: 100%; max-width: 1000px">
        <div v-if="!isSubmitted">
          <div v-if="!hasQuestion">
            <q-card class="column" style="padding: 32px; min-height: 500px; height: 100%">
              <div>
                <div v-if="loadingQuizMetadata" class="text-center q-pa-md">
                  <q-spinner color="primary" size="2em" />
                  <div class="q-mt-sm">กำลังโหลดข้อมูลแบบทดสอบ...</div>
                </div>
                <div v-else-if="quizMetadataError" class="text-center q-pa-md">
                  <q-icon name="error" color="negative" size="3em" />
                  <div class="text-negative q-mt-sm">{{ quizMetadataError }}</div>
                  <q-btn
                    color="primary"
                    label="ลองใหม่"
                    class="q-mt-md"
                    @click="loadQuizMetadata"
                  />
                </div>
                <div v-else>
                  <div class="row justify-between items-center">
                    <div class="text-h6 text-primary">{{ quiz.name }}</div>
                    <div class="text-h6 text-black">คะแนนเต็ม: {{ quiz.totalScore }}</div>
                  </div>
                  <div class="sub-body" style="color: black; margin-top: 50px">
                    แบบทดสอบ {{ quiz.name }}
                  </div>
                  <div v-if="quiz.timeout > 0" class="text-caption text-grey-7 q-mt-sm">
                    เวลาในการทำ:
                    <template v-if="Math.floor(quiz.timeout / 3600) > 0"
                      >{{ Math.floor(quiz.timeout / 3600) }} ชั่วโมง
                    </template>
                    <template v-if="Math.floor((quiz.timeout % 3600) / 60) > 0"
                      >{{ Math.floor((quiz.timeout % 3600) / 60) }} นาที
                    </template>
                    <template v-if="quiz.timeout % 60 > 0">{{ quiz.timeout % 60 }} วินาที</template>
                  </div>
                  <div v-if="quiz.totalQuestions > 0" class="text-caption text-grey-7">
                    จำนวนข้อ: {{ quiz.totalQuestions }} ข้อ
                  </div>
                  <div class="text-caption text-grey-7 q-mt-sm">
                    <template v-if="quiz.submitLimit === -1">
                      จำนวนครั้งที่ทำแล้ว: {{ userSubmissions.length }} ครั้ง (ไม่จำกัด)
                    </template>
                    <template v-else>
                      จำนวนครั้งที่ทำแล้ว: {{ userSubmissions.length }}/{{ quiz.submitLimit }}
                      ครั้ง
                    </template>
                  </div>
                </div>
              </div>
              <q-space />
              <div class="row justify-end">
                <q-btn
                  color="primary"
                  class="text-black"
                  label="เริ่มทำแบบทดสอบ"
                  :loading="loading || loadingQuizMetadata"
                  :disable="
                    loadingQuizMetadata || !quiz.name || userSubmissions.length >= quiz.submitLimit
                  "
                  @click="startQuiz"
                />
              </div>
            </q-card>
            <!-- User Submissions History -->
            <div v-if="userSubmissions.length > 0" class="q-mt-lg">
              <div class="text-subtitle1 text-primary q-mb-md">ประวัติการทำแบบทดสอบของคุณ</div>
              <div class="row q-gutter-md">
                <q-card
                  v-for="(submission, index) in userSubmissions"
                  :key="submission.submissionId"
                  class="q-pa-md"
                  style="min-width: 280px"
                  bordered
                >
                  <div class="text-subtitle2 q-mb-sm">
                    ครั้งที่ {{ userSubmissions.length - index }}
                  </div>
                  <div class="text-caption text-grey-7 q-mb-sm">
                    {{ new Date(submission.submissionDate).toLocaleString('th-TH') }}
                  </div>
                  <div class="row items-center justify-between q-mb-sm">
                    <div class="text-body2">
                      คะแนน: {{ submission.finalScore }}/{{ submission.totalScore }}
                    </div>
                    <q-chip
                      :color="submission.passed ? 'positive' : 'negative'"
                      text-color="white"
                      size="sm"
                    >
                      {{ submission.passed ? 'ผ่าน' : 'ไม่ผ่าน' }}
                    </q-chip>
                  </div>
                  <div class="text-caption text-grey-7 q-mb-md">
                    ใช้เวลา: {{ submission.timeSpent }}
                  </div>
                  <q-btn
                    color="primary"
                    size="sm"
                    label="ดูคะแนน"
                    class="full-width"
                    @click="viewQuizScore(submission.submissionId)"
                  />
                </q-card>
              </div>
            </div>
          </div>

          <div v-else class="q-my-md">
            <div v-if="currentQuestion">
              <div class="row justify-between items-center">
                <div class="text-h6 text-black">คำถาม {{ currentQuestionNo }}</div>
                <div class="text-h6 text-black">
                  เหลือ {{ Math.floor(timeLeft / 60) }}:{{ String(timeLeft % 60).padStart(2, '0') }}
                  นาที
                </div>
              </div>

              <div class="row justify-between items-start q-mt-md">
                <div class="text-h6 text-primary" style="margin-top: 8%">
                  {{ currentQuestion?.questions?.[0]?.questionText || 'กำลังโหลดคำถาม...' }}
                </div>
              </div>

              <q-card class="q-pa-md q-mt-md column" style="width: 100%; padding: 30px" bordered>
                <template v-if="currentQuestion?.type === QuestionType.RADIO">
                  <div v-for="option in currentQuestion.options" :key="option.id" class="q-mb-sm">
                    <div
                      class="q-pa-sm rounded-borders bg-grey-1 hover-card"
                      :class="{ selected: radioSelection === option.id }"
                    >
                      <q-radio
                        v-model="radioSelection"
                        :val="option.id"
                        :label="option.optionText"
                        class="full-width"
                        @blur="handleBlur()"
                      />
                    </div>
                  </div>
                </template>

                <template v-else-if="currentQuestion?.type === QuestionType.CHECKBOX">
                  <div v-for="option in currentQuestion.options" :key="option.id" class="q-mb-sm">
                    <div
                      class="q-pa-sm rounded-borders bg-grey-1 hover-card"
                      :class="{
                        selected: checkboxSelection.includes(option.id),
                      }"
                    >
                      <q-checkbox
                        v-model="checkboxSelection"
                        :val="option.id"
                        :label="option.optionText"
                        class="full-width"
                        @blur="handleBlur()"
                      />
                    </div>
                  </div>
                </template>

                <template v-else-if="currentQuestion?.type === QuestionType.TEXTFIELD">
                  <q-input
                    class="q-mt-md"
                    label="ตอบเพิ่มเติม"
                    v-model="textResponse"
                    type="textarea"
                    @blur="handleBlur()"
                  />
                </template>

                <div v-if="loading" class="text-center q-pa-md">
                  <q-spinner color="primary" size="2em" />
                  <div class="q-mt-sm">กำลังโหลด...</div>
                </div>
              </q-card>

              <div class="row justify-between items-center" style="margin-top: 80px">
                <q-btn
                  color="secondary"
                  label="ย้อน"
                  class="text-white"
                  style="width: 150px"
                  :disable="currentQuestionNo === 1 || loading"
                  :loading="loading"
                  @click="goToPrevQuestion"
                />
                <q-btn
                  v-if="!isLastQuestion"
                  label="ถัดไป"
                  color="primary"
                  class="text-black"
                  style="width: 150px"
                  :loading="loading"
                  @click="goToNextQuestion"
                />
                <q-btn
                  v-else
                  label="เสร็จสิ้น"
                  color="positive"
                  class="text-white"
                  style="width: 150px"
                  :loading="loading"
                  @click="submitQuiz"
                />
              </div>
            </div>
            <div v-else>
              <q-banner class="text-negative bg-red-1 q-mt-md">ไม่พบคำถาม</q-banner>
            </div>
          </div>
        </div>

        <div v-else>
          <q-card
            bordered
            class="q-pa-md column q-mt-md items-center justify-center"
            style="width: 100%; margin-top: 60px; padding: 30px; min-height: 300px"
          >
            <div class="text-h6 text-primary q-mb-sm">ท่านได้คะแนนทั้งหมด</div>
            <div class="text-h4 text-positive q-mb-sm">{{ quizScore?.score || totalCorrect }}</div>
            <div class="text-h6 text-primary q-mb-md">
              จากคะแนนเต็ม
              {{ quizScore?.totalScore || quiz.totalScore || quiz.totalQuestions }} คะแนน
            </div>
            <div class="sub-body surface q-mb-xs">
              ใช้เวลาไป
              <template v-if="timeUsed.minutes > 0"> {{ timeUsed.minutes }} นาที </template>
              <template v-if="timeUsed.seconds > 0"> {{ timeUsed.seconds }} วินาที </template>
              <template v-if="timeUsed.minutes === 0 && timeUsed.seconds === 0">
                น้อยกว่า 1 วินาที
              </template>
            </div>

            <div class="sub-body">บันทึกเมื่อ {{ new Date().toLocaleString() }}</div>
          </q-card>
          <div class="row justify-center q-mt-md">
            <q-btn
              color="primary"
              label="กลับหน้าหลัก"
              style="width: 150px"
              @click="backToQuiz()"
            />
          </div>
        </div>
      </div>
    </div>
    <DialogQuiz v-model="showDialog" @confirm="submitQuiz" />
  </q-page>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import { ref, computed, onMounted, watch } from 'vue';
import DialogQuiz from 'src/components/quiz/DialogQuiz.vue';
import type { ItemBlock, Submission, Assessment, Response as UserResponse } from 'src/types/models';
import { useAuthStore } from 'src/stores/auth';
import { QuestionType, type QuizScore, type UserSubmission } from 'src/types/quiz';
import { quizService } from 'src/services/quiz/quizAttemptsService';

const route = useRoute();
const router = useRouter();

const authStore = useAuthStore();

interface questionList {
  sequence: number;
  isDone: boolean;
}

// State management
const startTimestamp = ref<number | null>(null);
const hasQuestion = computed(() => !!route.query.no);
const currentQuestionNo = computed(() => Number(route.query.no || 1));
const showDialog = ref(false);
const loading = ref(false);
const loadingQuizMetadata = ref(false);
const submission = ref<Submission | null>(null);
const currentQuestion = ref<ItemBlock | null>(null);
const questionList = ref<questionList[] | null>(null);
const quizScore = ref<QuizScore | null>(null);
const quizMetadata = ref<Assessment | null>(null);
const quizMetadataError = ref<string | null>(null);
const userSubmissions = ref<UserSubmission[]>([]);

const linkUrl = route.params.linkUrl as string;

// User ID - in a real app, this would come from auth store
const userId = authStore.getCurrentUser()?.id;

// Quiz metadata computed from loaded assessment data
const quiz = computed(() => ({
  id: quizMetadata.value?.id || null,
  name: quizMetadata.value?.name || 'ไม่มีชื่อ',
  totalScore: quizMetadata.value?.totalScore || 0,
  timeout: quizMetadata.value?.timeout || 0,
  totalQuestions: questionList.value?.length || 0,
  passRatio: quizMetadata.value?.passRatio || 0.5,
  submitLimit: quizMetadata.value?.submitLimit || -1,
}));

const isLastQuestion = computed(() => currentQuestionNo.value >= quiz.value.totalQuestions);

const timeUsed = computed(() => {
  if (!startTimestamp.value) return { minutes: 0, seconds: 0 };
  const elapsedMs = Date.now() - startTimestamp.value;
  const totalSeconds = Math.floor(elapsedMs / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  return { minutes, seconds };
});

// Unified response state management for all question types
interface QuizResponse {
  optionId?: number;
  responseId?: number | undefined;
  textValue?: string;
}

// Unified state that handles radio, checkbox, and text responses
const quizResponses = ref<QuizResponse[]>([]);

// Track previous state for change detection (used for checkboxes)
const previousQuizResponses = ref<QuizResponse[]>([]);

// Computed properties for template v-model binding
const radioSelection = computed({
  get: () => {
    const response = quizResponses.value.find((r) => r.optionId !== undefined);
    return response?.optionId || null;
  },
  set: (optionId: number | null) => {
    if (optionId === null) {
      quizResponses.value = [];
    } else {
      const existingResponse = quizResponses.value.find((r) => r.optionId !== undefined);
      if (existingResponse) {
        existingResponse.optionId = optionId;
      } else {
        quizResponses.value = [{ optionId, responseId: undefined }];
      }
    }
  },
});

const checkboxSelection = computed({
  get: () => {
    return quizResponses.value.filter((r) => r.optionId !== undefined).map((r) => r.optionId!);
  },
  set: (optionIds: number[]) => {
    // Store previous state for change detection
    previousQuizResponses.value = [...quizResponses.value];

    // Keep existing responseIds for options that are still selected
    const existingMap = new Map(quizResponses.value.map((r) => [r.optionId, r.responseId]));

    quizResponses.value = optionIds.map((optionId) => ({
      optionId,
      responseId: existingMap.get(optionId) || undefined,
    }));
  },
});

const textResponse = computed({
  get: () => {
    const response = quizResponses.value.find((r) => r.textValue !== undefined);
    return response?.textValue || '';
  },
  set: (textValue: string) => {
    const existingResponse = quizResponses.value.find((r) => r.textValue !== undefined);
    if (existingResponse) {
      existingResponse.textValue = textValue;
    } else {
      quizResponses.value = [{ textValue, responseId: undefined }];
    }
  },
});

// Helper functions for unified state management
function getRadioResponse(): QuizResponse | undefined {
  return quizResponses.value.find((r) => r.optionId !== undefined);
}

function getTextResponse(): QuizResponse | undefined {
  return quizResponses.value.find((r) => r.textValue !== undefined);
}

function getCheckboxResponses(): QuizResponse[] {
  return quizResponses.value.filter((r) => r.optionId !== undefined);
}

const timeLeft = ref(0);
let timer: ReturnType<typeof setInterval> | null = null;

function startCountdown() {
  if (timer) clearInterval(timer);
  timer = setInterval(() => {
    if (timeLeft.value > 0) {
      timeLeft.value--;
    } else {
      clearInterval(timer!);
      showDialog.value = true;
      void submitQuiz();
    }
  }, 1000);
}

// Load quiz metadata from backend
async function loadQuizMetadata() {
  try {
    loadingQuizMetadata.value = true;
    quizMetadataError.value = null; // Clear any previous errors

    // Use the new endpoint that includes user submissions
    const headerData = await quizService.getQuizHeaderWithSubmissions(linkUrl, userId);
    quizMetadata.value = headerData.assessment;
    userSubmissions.value = headerData.userSubmissions;
  } catch (error) {
    console.error('Failed to load quiz metadata:', error);
    // Set error message and fallback values if loading fails
    quizMetadataError.value = 'ไม่สามารถโหลดข้อมูลแบบทดสอบได้ กรุณาลองใหม่อีกครั้ง';
    quizMetadata.value = null;
    userSubmissions.value = [];
  } finally {
    loadingQuizMetadata.value = false;
  }
}

async function startQuiz() {
  try {
    loading.value = true;

    submission.value = await quizService.startQuiz({
      linkUrl,
      userId: userId!,
    });

    console.log('✅ Quiz started successfully, submission:', submission.value);

    // Set timer from submission data or quiz metadata
    if (submission.value.endAt) {
      const endTime = new Date(submission.value.endAt).getTime();
      const currentTime = Date.now();
      timeLeft.value = Math.max(0, Math.floor((endTime - currentTime) / 1000));
    } else if (quiz.value.timeout > 0) {
      // Fallback to quiz timeout if submission doesn't have endAt
      timeLeft.value = quiz.value.timeout;
    }

    startTimestamp.value = Date.now();
    isSubmitted.value = false;
    totalCorrect.value = 0;
    startCountdown();

    // Navigate to first question or restore from localStorage
    const questionNo = Number(localStorage.getItem('currentQuestionNo')) || 1;
    await navigateToQuestion(questionNo);
    return true;
  } catch (error) {
    console.error('Failed to start quiz:', error);
  } finally {
    loading.value = false;
  }
}

// Helper function to navigate to a specific question
async function navigateToQuestion(questionNo: number) {
  await loadQuestion(questionNo);
  localStorage.setItem('currentQuestionNo', questionNo.toString());
  await router.push({ path: route.path, query: { no: questionNo } });
}

async function goToNextQuestion() {
  const nextNo = currentQuestionNo.value + 1;
  await navigateToQuestion(nextNo);
}

async function goToPrevQuestion() {
  const prevNo = Math.max(1, currentQuestionNo.value - 1);
  await navigateToQuestion(prevNo);
}

// Load question data from API
async function loadQuestion(questionNo: number) {
  if (!submission.value) return;

  try {
    loading.value = true;

    // Get the complete question metadata including user responses
    const questionMeta = await quizService.getQuestion(submission.value.id, questionNo);

    // Extract the question block and response data
    currentQuestion.value = questionMeta.questionBlock;
    questionList.value = questionMeta.questionList;

    // Update quiz metadata if not already set
    if (!quiz.value.totalQuestions && questionList.value) {
      quiz.value.totalQuestions = questionList.value.length;
    }

    // Pre-populate user responses if they exist
    populateUserResponses(questionMeta.response);
  } catch (error) {
    console.error('Failed to load question:', error);
  } finally {
    loading.value = false;
  }
}

// Clear all response states when navigating between questions
function clearResponseStates() {
  // Clear all responses using unified state
  quizResponses.value = [];
  previousQuizResponses.value = [];
}

// Populate user responses for the current question
function populateUserResponses(userResponses: UserResponse[] | null) {
  // Clear previous states first
  clearResponseStates();

  if (!userResponses || !userResponses.length) {
    return;
  }

  const questionType = currentQuestion.value?.type;

  console.log('populateUserResponses', userResponses);

  // Handle different question types using unified state
  if (questionType === QuestionType.RADIO) {
    // For radio buttons, set the selected option
    const response = userResponses[0]; // Radio should have only one response
    if (response?.selectedOptionId) {
      quizResponses.value = [
        {
          optionId: response.selectedOptionId,
          responseId: response.id,
        },
      ];
    }
  } else if (questionType === QuestionType.CHECKBOX) {
    // For checkboxes, set all selected options
    quizResponses.value = userResponses.map((response) => ({
      optionId: response.selectedOptionId!,
      responseId: response.id,
    }));
    // Initialize previous state
    previousQuizResponses.value = [...quizResponses.value];
  } else if (questionType === QuestionType.TEXTFIELD) {
    // For text fields, set the answer text
    const response = userResponses[0]; // Text field should have only one response
    if (response?.selectedOption) {
      quizResponses.value = [
        {
          textValue: response.selectedOption.optionText,
          responseId: response.id,
        },
      ];
    }
  }
}

// Simplified error handling
function validateQuizState(): { isValid: boolean; error?: string } {
  if (!submission.value) {
    return { isValid: false, error: 'ไม่พบข้อมูลการส่งแบบทดสอบ' };
  }
  if (!currentQuestion.value?.questions?.[0]?.id) {
    return { isValid: false, error: 'ไม่พบข้อมูลคำถาม' };
  }
  return { isValid: true };
}

// Save radio button answer
async function saveRadioAnswer(
  submissionId: number,
  questionId: number,
  selectedValue: number | null,
  responseId?: number,
): Promise<UserResponse> {
  try {
    return await quizService.saveQuizResponse({
      submissionId,
      questionId,
      responseId,
      selectedOptionId: selectedValue!,
    });
  } catch (error) {
    console.error('❌ Failed to save radio answer:', error);
    throw error;
  }
}

// Simplified checkbox operations
async function processCheckboxResponse(
  operation: 'create' | 'update' | 'delete',
  submissionId: number,
  questionId: number,
  optionId?: number,
  responseId?: number,
): Promise<UserResponse | void> {
  try {
    switch (operation) {
      case 'create': {
        const createResponse = await quizService.saveQuizResponse({
          submissionId,
          questionId,
          selectedOptionId: optionId!,
        });
        console.log('✅ Created checkbox response:', { optionId, responseId: createResponse.id });
        return createResponse;
      }

      case 'update': {
        const updateResponse = await quizService.saveQuizResponse({
          submissionId,
          questionId,
          responseId,
          selectedOptionId: optionId!,
        });
        console.log('✅ Updated checkbox response:', { responseId, optionId });
        return updateResponse;
      }

      case 'delete':
        await quizService.deleteQuizResponse(responseId!);
        console.log('✅ Deleted checkbox response:', { responseId });
        break;
    }
  } catch (error) {
    console.error(`❌ Failed to ${operation} checkbox response:`, { optionId, responseId, error });
    throw error;
  }
}

// Enhanced checkbox answers management with create, update, and delete operations
async function saveCheckboxAnswers(
  submissionId: number,
  questionId: number,
  currentSelections: QuizResponse[],
): Promise<void> {
  try {
    console.log('🔄 Processing checkbox changes:', {
      previous: previousQuizResponses.value,
      current: currentSelections,
    });

    // Detect changes by comparing current and previous selections
    const previousIds = new Set(previousQuizResponses.value.map((item) => item.optionId));
    const currentIds = new Set(currentSelections.map((item) => item.optionId));

    const newSelections = currentSelections.filter((item) => !previousIds.has(item.optionId));
    const existingSelections = currentSelections.filter((item) => previousIds.has(item.optionId));
    const deselections = previousQuizResponses.value.filter(
      (item) => !currentIds.has(item.optionId),
    );

    console.log('📊 Detected changes:', { newSelections, existingSelections, deselections });

    // Process all changes concurrently
    const operations: Promise<UserResponse | void>[] = [];

    // Handle new selections (create new responses)
    for (const newSelection of newSelections) {
      operations.push(
        processCheckboxResponse('create', submissionId, questionId, newSelection.optionId),
      );
    }

    // Handle existing selections (update if needed)
    for (const existingSelection of existingSelections) {
      if (existingSelection.responseId) {
        operations.push(
          processCheckboxResponse(
            'update',
            submissionId,
            questionId,
            existingSelection.optionId,
            existingSelection.responseId,
          ),
        );
      }
    }

    // Handle deselections (delete responses)
    for (const deselection of deselections) {
      if (deselection.responseId) {
        operations.push(
          processCheckboxResponse(
            'delete',
            submissionId,
            questionId,
            undefined,
            deselection.responseId,
          ),
        );
      }
    }

    // Execute all operations
    const results = await Promise.allSettled(operations);

    // Update response IDs for new selections
    const newResponseResults = results.slice(0, newSelections.length);
    newResponseResults.forEach((result, index) => {
      if (
        result.status === 'fulfilled' &&
        result.value &&
        'id' in result.value &&
        result.value.id
      ) {
        const optionId = newSelections[index]?.optionId;
        if (optionId && result.value) {
          const optionIndex = quizResponses.value.findIndex((opt) => opt.optionId === optionId);
          if (optionIndex !== -1) {
            quizResponses.value[optionIndex]!.responseId = result.value.id;
          }
        }
      }
    });

    // Check for any failures and provide detailed error information
    const failures = results.filter((result) => result.status === 'rejected');
    if (failures.length > 0) {
      console.error('❌ Some checkbox operations failed:', failures);

      // Log specific failure details
      failures.forEach((failure, index) => {
        if (failure.status === 'rejected') {
          console.error(`Operation ${index} failed:`, failure.reason);
        }
      });

      // Don't throw error for partial failures - some operations may have succeeded
      console.warn(`⚠️ ${failures.length} out of ${results.length} checkbox operations failed`);
    }

    console.log('✅ Checkbox operations completed:', {
      total: results.length,
      successful: results.length - failures.length,
      failed: failures.length,
    });

    // Update previous state for next comparison
    previousQuizResponses.value = [...quizResponses.value];
  } catch (error) {
    console.error('❌ Failed to save checkbox answers:', error);
    throw error;
  }
}

// Save text field answer
async function saveTextAnswer(
  submissionId: number,
  questionId: number,
  textValue: string,
  responseId?: number,
): Promise<void> {
  try {
    await quizService.saveQuizResponse({
      submissionId,
      questionId,
      answerText: textValue.trim(),
      responseId,
    });
    console.log('✅ Text answer saved successfully:', { questionId, textValue: textValue.trim() });
  } catch (error) {
    console.error('❌ Failed to save text answer:', error);
    throw error;
  }
}

// Main save function with simplified error handling
async function saveCurrentAnswer(): Promise<void> {
  // Validate basic requirements
  const validation = validateQuizState();
  if (!validation.isValid) {
    console.warn('⚠️ Validation failed:', validation.error);
    return; // Silent return for validation failures during blur events
  }

  const submissionId = submission.value!.id;
  const questionId = currentQuestion.value!.questions?.[0]?.id;
  const questionType = currentQuestion.value!.type;

  if (!questionId) {
    console.warn('⚠️ Question ID not found');
    return;
  }

  try {
    // Save based on question type using unified state
    switch (questionType) {
      case QuestionType.RADIO: {
        const radioResponse = getRadioResponse();
        if (radioResponse) {
          await saveRadioAnswer(
            submissionId,
            questionId,
            radioResponse.optionId!,
            radioResponse.responseId,
          );
        }
        break;
      }

      case QuestionType.CHECKBOX:
        await saveCheckboxAnswers(submissionId, questionId, getCheckboxResponses());
        break;

      case QuestionType.TEXTFIELD: {
        const textResponse = getTextResponse();
        if (textResponse) {
          await saveTextAnswer(
            submissionId,
            questionId,
            textResponse.textValue!,
            textResponse.responseId,
          );
        }
        break;
      }

      default:
        console.warn('⚠️ Unknown question type:', questionType);
        return;
    }

    console.log('✅ Answer saved successfully for question:', questionId);
  } catch (error) {
    console.error('❌ Failed to save answer:', error);
    // Could optionally show a subtle notification here for critical errors
  }
}

const isSubmitted = ref(false);
const totalCorrect = ref(0);

async function submitQuiz() {
  if (!submission.value) return;

  try {
    loading.value = true;

    // Save current answer before submitting
    await saveCurrentAnswer();

    // Stop the timer
    if (timer) clearInterval(timer);

    // Submit the assessment
    await quizService.submitAssessment(submission.value.id);

    // Get the quiz score
    quizScore.value = await quizService.getQuizScore(submission.value.id);

    // clear localStorage
    localStorage.removeItem('currentQuestionNo');

    // Update UI state
    totalCorrect.value = quizScore.value.score;
    isSubmitted.value = true;
  } catch (error) {
    console.error('Failed to submit quiz:', error);
  } finally {
    loading.value = false;
  }
}

async function backToQuiz() {
  await router.push(`/quiz/${linkUrl}/preview`);
  // then reload the page
  window.location.reload();
}

// Watch for route changes to load questions
watch(
  () => submission.value,
  async () => {
    if (route.query.no) {
      await loadQuestion(Number(route.query.no));
    }
  },
  { immediate: true },
);

// handle blur event
const handleBlur = async () => {
  // wait for 1 second
  await new Promise((resolve) => setTimeout(resolve, 200));
  await saveCurrentAnswer();
};

// Function to view quiz score for a specific submission
async function viewQuizScore(submissionId: number) {
  try {
    loading.value = true;
    const score = await quizService.getQuizScore(submissionId);

    // You can show this in a dialog or navigate to a score page
    console.log('Quiz score:', score);

    // For now, let's show an alert with the score information
    alert(
      `คะแนน: ${score.score}/${score.totalScore} คะแนน\nสถานะ: ${score.isPassed ? 'ผ่าน' : 'ไม่ผ่าน'}`,
    );
  } catch (error) {
    console.error('Failed to get quiz score:', error);
  } finally {
    loading.value = false;
  }
}

// Initialize component
onMounted(async () => {
  // loading
  loading.value = true;

  // If we have a question number in the route, we're already in quiz mode
  if (route.query.no && !submission.value) {
    // This happens when user refreshes the page during quiz
    console.log('Page refresh detected during quiz, attempting to restore session...');

    // Try to restore the quiz session using the linkUrl
    const sessionRestored = await startQuiz();

    if (sessionRestored) {
      console.log('Quiz session restored successfully');
      // The route watcher will automatically load the current question
    } else {
      console.warn('Failed to restore quiz session, redirecting to start');
      // Remove the question number from the route to go back to start screen
      await router.push({ path: route.path });
    }
  } else {
    await loadQuizMetadata();
  }
  loading.value = false;
});
</script>

<style lang="scss" scoped>
$accent-selected: #f1ebf8;

.bg-accent-selected {
  background-color: $accent-selected;
  border-radius: 12px;
}

.hover-card {
  transition: background-color 0.3s ease;
  cursor: pointer;

  &:hover {
    background-color: #e0e0e0;
  }
}

.selected {
  background-color: hsl(46, 98%, 78%) !important;
}
</style>
