<template>
  <q-dialog ref="dialogRef" persistent @hide="onDialogHide">
    <q-card class="card-form" bordered flat style="width: 1024px">
      <q-form ref="formRef" @submit.prevent="onClickSave()">
        <q-card-section class="text-h6"> {{ computedTitle }}</q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <q-input color="accent" filled v-model="formData.name" label="ชื่อ" />
          <q-input color="accent" filled v-model="formData.email" label="อีเมล" />
          <q-select
            color="accent"
            filled
            v-model="formData.roles"
            :options="roleOptions"
            label="บทบาท"
            multiple
            option-label="name"
            option-value="id"
            use-chips
          />
          <q-input
            filled
            v-model="formData.password"
            label="รหัสผ่าน"
            color="accent"
            type="password"
          />
        </q-card-section>
        <q-card-actions align="right" class="q-px-md">
          <q-btn color="secondary" label="ยกเลิก" flat @click="onClickCancel" />
          <q-btn type="submit" color="accent" label="บันทึก" @click="onClickSave" />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { QForm, useDialogPluginComponent } from 'quasar';
import type { User } from 'src/types/models';
import { computed, ref } from 'vue';

const props = defineProps<{
  user?: User;
}>();

const { dialogRef, onDialogHide, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const formRef = ref<typeof QForm>();

const computedTitle = computed(() => {
  return props.user ? `แก้ไขผู้ใช้งาน: ${props.user.name}` : 'สร้างผู้ใช้งานใหม่';
});

const onClickSave = () => {
  if (!formRef.value) {
    console.error('Form reference is not set');
    return;
  }
  // Validate form data here if needed
  formRef.value.validate();
  onDialogOK(formData.value);
};

const onClickCancel = () => {
  onDialogCancel();
};

const formData = ref<Partial<User>>({ ...props.user });

const roleOptions = ref([
  { id: 1, name: 'Admin' },
  { id: 2, name: 'Editor' },
  { id: 3, name: 'Manager' },
]);
</script>

<style scoped>
.q-btn {
  min-width: 100px;
}
</style>
