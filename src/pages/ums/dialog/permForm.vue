<template>
  <q-dialog ref="dialogRef" persistent @hide="onDialogHide">
    <q-card class="card-form" bordered flat style="width: 1024px">
      <q-form ref="formRef" @submit.prevent="onClickSave()">
        <q-card-section class="text-h6"> {{ computedTitle }}</q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <q-input color="accent" filled v-model="formData.name" label="ชื่อ" />
          <q-input color="accent" filled v-model="formData.nameEn" label="ชื่ออังกฤษ" />
          <div class="row">
            <q-toggle class="col-12" label="สามารถใช้ได้" v-model="formData.status" />
            <q-toggle class="col-12" label="เป็นค่าเริ่นต้น" v-model="formData.isDefault" />
          </div>
        </q-card-section>
        <q-card-actions align="right" class="q-px-md">
          <q-btn color="secondary" label="ยกเลิก" flat @click="onClickCancel" />
          <q-btn type="submit" color="accent" label="บันทึก" @click="onClickSave" />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { QForm, useDialogPluginComponent } from 'quasar';
import type { Permission } from 'src/types/models';
import { computed, ref } from 'vue';

const props = defineProps<{
  permission?: Permission;
}>();

const { dialogRef, onDialogHide, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const formRef = ref<typeof QForm>();

const computedTitle = computed(() => {
  return props.permission
    ? `แก้ไขสิทธิ์การเข้าถึง: ${props.permission.name}`
    : 'สร้างสิทธิ์การเข้าถึงใหม่';
});

const onClickSave = () => {
  if (!formRef.value) {
    console.error('Form reference is not set');
    return;
  }
  // Validate form data here if needed
  formRef.value.validate();
  onDialogOK(formData.value);
};

const onClickCancel = () => {
  onDialogCancel();
};

const formData = ref<Partial<Permission>>({ ...props.permission });
</script>

<style scoped>
.q-btn {
  min-width: 100px;
}
</style>
