<template>
  <q-page padding>
    <q-table
      title="จัดการสิทธิ์"
      v-model:pagination="pagination"
      :rows
      :columns="permColumns"
      row-key="id"
      @request="onRequest"
    >
      <template #top-right>
        <div>
          <q-btn color="accent" label="เพิ่มสิทธิ์" icon="add" @click="onClickNewPermission" />
        </div>
      </template>
      <template #body-cell-actions="props">
        <q-td class="row justify-center">
          <q-btn icon="edit" padding="xs" @click="onClickEdit(props.row as Permission)" />
          <q-btn icon="delete" padding="xs" @click="onClickDelete(props.row as Permission)"></q-btn>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup lang="ts">
import { type QTableProps, useQuasar } from 'quasar';
import { defaultPaginationValue } from 'src/configs/app.config';
import { permColumns } from 'src/data/table_columns';
import { PermissionService } from 'src/services/ums/permissionService';
import type { Permission } from 'src/types/models';
import { defineAsyncComponent, onMounted, ref } from 'vue';

const pagination = ref({ ...defaultPaginationValue });
const rows = ref<Permission[]>([]);
const onRequest: QTableProps['onRequest'] = ({ pagination }) => {
  PermissionService.getPermissions(pagination)
    .then((res) => {
      rows.value = res.data.data;
    })
    .catch((error: unknown) => {
      console.error('Error fetching permissions:', error);
    });
};

onMounted(() => {
  PermissionService.getPermissions(pagination.value)
    .then((res: { data: { data: Permission[] } }) => {
      rows.value = res.data.data || [];
    })
    .catch((error: unknown) => {
      console.error('Error fetching permissions:', error);
    });
});

const $q = useQuasar();

const onClickNewPermission = () => {
  $q.dialog({
    component: defineAsyncComponent(() => import('../dialog/permForm.vue')),
    persistent: true,
    componentProps: {
      permission: null, // Pass null for new permission
    },
  }).onOk((payload: Permission) => {
    if (payload) {
      PermissionService.createPermission(payload)
        .then((res: { data: Permission }) => {
          rows.value.push(res.data); // Add the new permission to the table
          $q.notify({
            type: 'positive',
            message: 'สิทธิ์ถูกสร้างเรียบร้อยแล้ว',
          });
        })
        .catch((error: unknown) => {
          console.error('Error creating permission:', error);
          $q.notify({
            type: 'negative',
            message: 'เกิดข้อผิดพลาดในการสร้างสิทธิ์',
          });
        });
    }
  });
};

const onClickEdit = (permission: Permission) => {
  $q.dialog({
    component: defineAsyncComponent(() => import('../dialog/permForm.vue')),
    persistent: true,
    componentProps: {
      permission, // Pass the selected permission for editing
    },
  }).onOk((payload: Permission) => {
    if (payload) {
      PermissionService.updatePermission(permission.id, payload)
        .then((res: { data: Permission }) => {
          const index = rows.value.findIndex((p) => p.id === res.data.id);
          if (index !== -1) {
            rows.value[index] = res.data; // Update the permission in the table
          }
          $q.notify({
            type: 'positive',
            message: 'สิทธิ์ถูกแก้ไขเรียบร้อยแล้ว',
          });
        })
        .catch((error: unknown) => {
          console.error('Error updating permission:', error);
          $q.notify({
            type: 'negative',
            message: 'เกิดข้อผิดพลาดในการแก้ไขสิทธิ์',
          });
        });
    }
  });
};

const onClickDelete = (permission: Permission) => {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: `คุณแน่ใจหรือไม่ว่าต้องการลบสิทธิ์: ${permission.name}?`,
    cancel: true,
    persistent: true,
  }).onOk(() => {
    PermissionService.deletePermission(permission.id)
      .then(() => {
        rows.value = rows.value.filter((p) => p.id !== permission.id); // Remove the permission from the table
        $q.notify({
          type: 'positive',
          message: 'สิทธิ์ถูกลบเรียบร้อยแล้ว',
        });
      })
      .catch((error: unknown) => {
        console.error('Error deleting permission:', error);
        $q.notify({
          type: 'negative',
          message: 'เกิดข้อผิดพลาดในการลบสิทธิ์',
        });
      });
  });
};
</script>

<style scoped></style>
