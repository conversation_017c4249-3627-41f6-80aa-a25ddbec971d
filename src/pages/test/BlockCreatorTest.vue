<template>
  <q-page class="q-pa-md">
    <div class="row items-center justify-between q-mb-md">
      <div class="text-h4">BlockCreator Test Page</div>
      <div class="row q-gutter-sm">
        <q-btn label="Reset to Default" color="primary" @click="resetToDefaultBlocks" icon="home" />
        <q-btn label="Load Mock Data" color="secondary" @click="resetToMockData" icon="refresh" />
        <q-btn label="Clear All Blocks" color="negative" @click="clearAllBlocks" icon="clear" />
        <q-btn label="Show Block Order" color="info" @click="showBlockOrder" icon="list" />
      </div>
    </div>

    <q-separator class="q-mb-md" />

    <!-- Test with mock data -->
    <div class="q-mb-md">
      <div class="text-h6 q-mb-sm">
        Current Blocks: {{ blocks.length }} | Sections: {{ uniqueSections }}
      </div>
      <div class="text-body2 text-grey-7 q-mb-sm">
        This page tests the refactored BlockCreator component. It starts with default blocks (1
        header + 1 radio item). You can add, duplicate, delete blocks and test section creation.
      </div>
      <div class="text-body2 text-primary">
        <strong>Section Testing:</strong> Click "Add Section" to create new sections. New sections
        will always be added to the end of the form, regardless of current focus position.
      </div>
      <div class="text-body2 text-info q-mt-sm">
        <strong>Drag & Drop:</strong> Use the three-dot menu (⋮) at the top of each block to drag
        and reorder blocks within the form.
      </div>
      <div class="text-body2 text-accent q-mt-sm">
        <strong>Three-Dot Menu:</strong> Each block has a borderless three-dot menu (⋮) at the top
        that serves as both the drag handle and action menu with duplicate and delete options.
      </div>
      <div class="text-body2 text-positive q-mt-sm">
        <strong>Expected Behavior:</strong> Blocks should maintain their data integrity when
        reordered, and the three-dot menu should provide easy access to block actions.
      </div>
    </div>

    <!-- BlockCreator Component -->
    <BlockCreator :blocks="blocks" type="evaluate" :assessment-id="1" />
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import BlockCreator from 'src/components/common/blocks/BlockCreator.vue';
import { mockItemBlocks } from 'src/data/mock';
import { defaultBlocks } from 'src/data/defaultBlocks';
import type { ItemBlock } from 'src/types/models';

// Define component name for keep-alive
defineOptions({
  name: 'block-creator-test',
});

// Reactive blocks data - start with default blocks
const blocks = ref<ItemBlock[]>([...defaultBlocks]);

// Computed properties for testing
const uniqueSections = computed(() => {
  const sectionNumbers = new Set(blocks.value.map((block) => block.section));
  return sectionNumbers.size;
});

// Test functions
const resetToMockData = () => {
  blocks.value = [...mockItemBlocks];
};

const resetToDefaultBlocks = () => {
  blocks.value = [...defaultBlocks];
};

const clearAllBlocks = () => {
  blocks.value = [];
};

const showBlockOrder = () => {
  const blockInfo = blocks.value.map((block, index) => ({
    index: index + 1,
    id: block.id,
    type: block.type,
    sequence: block.sequence,
    section: block.section,
  }));

  console.table(blockInfo);
  alert(`Block Order:\n${JSON.stringify(blockInfo, null, 2)}`);
};
</script>

<style scoped>
.q-page {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
