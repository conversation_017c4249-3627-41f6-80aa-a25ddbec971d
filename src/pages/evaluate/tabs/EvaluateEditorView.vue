<template>
  <q-page class="q-pa-md">
    <div v-if="!blockCreatorStore.currentAssessment" class="text-center q-pa-lg">
      <q-spinner size="lg" color="primary" />
      <div class="q-mt-md">Loading assessment...</div>
    </div>
    <div v-else>
      <!-- Debug info for development
      <div v-if="showDebugInfo" class="q-mb-md q-pa-md bg-grey-2 rounded-borders">
        <div class="row items-center justify-between q-mb-sm">
          <div class="text-h6">Debug Info:</div>
          <q-btn
            size="sm"
            color="primary"
            label="View Assessment JSON"
            @click="logAssessmentJSON"
          />
        </div>
        <div>Assessment ID: {{ assessmentId }}</div>
        <div>Blocks Count: {{ blocks.length }}</div>
        <div>Block IDs: {{ blocks.map((b) => b.id).join(', ') }}</div>
        <div>ID Validation: {{ idValidation.valid ? '✅ Valid' : '❌ Invalid' }}</div>
        <div v-if="!idValidation.valid" class="text-negative">
          Missing: {{ idValidation.missing.join(', ') }}
        </div>
      </div> -->

      <BlockCreator :blocks="blocks" :assessmentId="assessmentId" type="evaluate" />
    </div>
  </q-page>
</template>

<script setup lang="ts">
import BlockCreator from 'src/components/common/blocks/BlockCreator.vue';
import { computed, onMounted } from 'vue';
import { useBlockCreatorStore } from 'src/stores/block_creator';

const blockCreatorStore = useBlockCreatorStore();

// Show debug info in development
// const showDebugInfo = ref(process.env.NODE_ENV === 'development');

// Get assessment ID from store
const assessmentId = computed(() => blockCreatorStore.currentAssessment?.id || null);

// Get blocks from assessment - this is the key fix!
const blocks = computed(() => {
  const assessmentBlocks = blockCreatorStore.currentAssessment?.itemBlocks;

  if (assessmentBlocks && assessmentBlocks.length > 0) {
    return assessmentBlocks;
  } else {
    return [];
  }
});

onMounted(() => {});

// ID validation computed property
// const idValidation = computed(() => {
//   return evaluateFormStore.validateIds();
// });

// // Debug function to log assessment JSON
// const logAssessmentJSON = () => {
//   console.log('=== FULL ASSESSMENT JSON ===');
//   console.log(JSON.stringify(evaluateFormStore.currentAssessment, null, 2));
//   console.log('=== ID TRACKING INFO ===');
//   console.log({
//     assessmentId: evaluateFormStore.getAssessmentId(),
//     allBlockIds: evaluateFormStore.getAllItemBlockIds(),
//     headerBlockId: evaluateFormStore.getHeaderBlockId(),
//     radioBlockId: evaluateFormStore.getRadioBlockId(),
//     validation: evaluateFormStore.validateIds(),
//   });
// };
</script>
<style scoped></style>
