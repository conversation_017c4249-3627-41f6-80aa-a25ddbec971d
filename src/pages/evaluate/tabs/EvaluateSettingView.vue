<script setup lang="ts">
import {
  computed,
  ref,
  watch,
  onMounted,
  onActivated,
  onDeactivated,
  onUnmounted,
  nextTick,
} from 'vue';
import { useBlockCreatorStore } from 'src/stores/block_creator';
import SelectDate from 'src/components/common/SelectDate.vue';
import Toggle from 'src/components/common/ToggleBtn.vue';
import { useRoute } from 'vue-router';
import { useGlobalStore } from 'src/stores/global';

// const router = useRouter();
const route = useRoute();
const paramId = route.params.id as string;
const blockCreatorStore = useBlockCreatorStore();
const globalStore = useGlobalStore();

// Create reactive refs for the form fields
const startDate = ref('');
const endDate = ref('');
const responseEdit = ref(false);
const subLimit = ref<number | null>(null);
const globalIsRequired = ref(false); // Added for global toggle of isRequired
const isPrototype = ref(false);

// Flag to prevent circular updates during programmatic changes
const isUpdatingFromStore = ref(false);

// Debounce timeout for saving
const saveTimeout = ref<number | null>(null);

// Helper function to calculate the actual global isRequired state based on itemBlocks
const calculateActualGlobalIsRequired = (): boolean => {
  if (!blockCreatorStore.currentAssessment?.itemBlocks) {
    return false;
  }

  const eligibleBlocks = blockCreatorStore.currentAssessment.itemBlocks.filter(
    (block) => block.type !== 'HEADER' && block.type !== 'IMAGE',
  );

  if (eligibleBlocks.length === 0) {
    return false;
  }

  return eligibleBlocks.every((block) => block.isRequired === true);
};

// Initialize form fields from assessment
// ! implemented new service later
onMounted(async () => {
  await blockCreatorStore.fetchAssessmentById(Number(paramId));
  if (blockCreatorStore.currentAssessment) {
    // Set flag to prevent watchers from triggering during initialization
    isUpdatingFromStore.value = true;

    startDate.value = blockCreatorStore.currentAssessment.startAt || '';
    endDate.value = blockCreatorStore.currentAssessment.endAt || '';
    responseEdit.value = blockCreatorStore.currentAssessment.responseEdit || false;
    subLimit.value =
      blockCreatorStore.currentAssessment.submitLimit !== undefined
        ? blockCreatorStore.currentAssessment.submitLimit
        : null;

    // Calculate the actual global isRequired state based on current itemBlocks
    const actualGlobalIsRequired = calculateActualGlobalIsRequired();
    globalIsRequired.value = actualGlobalIsRequired;

    // Update the stored value if it doesn't match the actual state
    if (blockCreatorStore.currentAssessment.globalIsRequired !== actualGlobalIsRequired) {
      blockCreatorStore.currentAssessment.globalIsRequired = actualGlobalIsRequired;
      console.log(
        `🔄 Corrected globalIsRequired from stored value to actual state: ${actualGlobalIsRequired}`,
      );
    }

    isPrototype.value = blockCreatorStore.currentAssessment.isPrototype || false;

    // Reset flag after initialization
    void nextTick(() => {
      isUpdatingFromStore.value = false;
    });
  } else {
    console.log('No assessment in store, creating default assessment');
    startDate.value = '';
    endDate.value = '';
    responseEdit.value = false;
    subLimit.value = null;
  }
});

onUnmounted(() => {
  console.log('SettingEvaluate component unmounted');

  // Clear any pending save timeout
  if (saveTimeout.value) {
    clearTimeout(saveTimeout.value);
    saveTimeout.value = null;
  }

  // Note: We don't clear the assessment here as it should persist for navigation
  console.log('Cleanup completed');
});

// Watch for changes in the assessment and update local state
watch(
  () => blockCreatorStore.currentAssessment,
  (newAssessment) => {
    if (newAssessment && !isUpdatingFromStore.value) {
      console.log('🔄 Updating local state from store assessment');

      // Set flag to prevent circular updates
      isUpdatingFromStore.value = true;

      // Update local state with new assessment values
      startDate.value = newAssessment.startAt || '';
      endDate.value = newAssessment.endAt || '';
      responseEdit.value = newAssessment.responseEdit || false;
      subLimit.value = newAssessment.submitLimit !== undefined ? newAssessment.submitLimit : null;

      // Calculate the actual global isRequired state based on current itemBlocks
      const actualGlobalIsRequired = calculateActualGlobalIsRequired();
      globalIsRequired.value = actualGlobalIsRequired;

      // Reset flag after a short delay to allow for reactive updates
      void nextTick(() => {
        isUpdatingFromStore.value = false;
      });
    }
  },
  { deep: true }, // Add deep watching to detect nested property changes
);

// Update the assessment when form fields change
watch(startDate, (newValue) => {
  if (isUpdatingFromStore.value || !blockCreatorStore.currentAssessment) {
    return;
  }

  console.log('Start date changed by user:', newValue);
  // Handle empty string case
  if (newValue) {
    blockCreatorStore.currentAssessment.startAt = newValue;
  } else {
    delete blockCreatorStore.currentAssessment.startAt;
  }
  debouncedSaveAssessment();
});

watch(endDate, (newValue) => {
  if (isUpdatingFromStore.value || !blockCreatorStore.currentAssessment) {
    return;
  }

  console.log('End date changed by user:', newValue);
  // Handle empty string case
  if (newValue) {
    blockCreatorStore.currentAssessment.endAt = newValue;
  } else {
    delete blockCreatorStore.currentAssessment.endAt;
  }
  debouncedSaveAssessment();
});

watch(responseEdit, (newValue) => {
  if (isUpdatingFromStore.value || !blockCreatorStore.currentAssessment) {
    return;
  }

  console.log('Response edit changed by user:', newValue);
  // Ensure we're setting a boolean value
  blockCreatorStore.currentAssessment.responseEdit = Boolean(newValue);
  debouncedSaveAssessment();
});

watch(subLimit, (newValue) => {
  if (isUpdatingFromStore.value || !blockCreatorStore.currentAssessment) {
    return;
  }

  console.log('Submission limit changed by user:', newValue);
  blockCreatorStore.currentAssessment.submitLimit = Number(newValue);
  debouncedSaveAssessment();
});

// Watch for changes in the global isRequired toggle
watch(globalIsRequired, async (newValue) => {
  // Skip if this is a programmatic update from the store
  if (isUpdatingFromStore.value || !blockCreatorStore.currentAssessment) {
    return;
  }

  console.log('Global isRequired changed by user:', newValue);

  // Update the globalIsRequired property in the assessment
  blockCreatorStore.currentAssessment.globalIsRequired = Boolean(newValue);

  // Update all itemBlocks to match the global setting
  if (blockCreatorStore.currentAssessment.itemBlocks) {
    // Import AssessmentService for API calls
    const { AssessmentService } = await import('src/services/asm/assessmentService');
    const assessmentService = new AssessmentService('evaluate');

    // Update each eligible block both locally and via API
    const updatePromises = blockCreatorStore.currentAssessment.itemBlocks
      .filter((itemBlock) => itemBlock.type !== 'HEADER' && itemBlock.type !== 'IMAGE')
      .map(async (itemBlock) => {
        // Update local state first (optimistic update)
        itemBlock.isRequired = Boolean(newValue);

        try {
          // Update via API
          const updatedBlock = await assessmentService.updateBlock({
            ...itemBlock,
            isRequired: Boolean(newValue),
          });

          if (updatedBlock) {
            console.log(`✅ Updated block ${itemBlock.id} isRequired to ${newValue} via API`);
            // Update with API response (in case there are differences)
            Object.assign(itemBlock, updatedBlock);
          }
        } catch (error) {
          console.error(`❌ Failed to update block ${itemBlock.id} via API:`, error);
          // Note: We don't revert here as the global operation should continue
        }
      });

    // Wait for all API calls to complete
    await Promise.allSettled(updatePromises);

    console.log('Updated isRequired for all itemBlocks:', newValue);

    // Trigger reactivity
    blockCreatorStore.currentAssessment.itemBlocks = [
      ...blockCreatorStore.currentAssessment.itemBlocks,
    ];
  }

  // Use debounced save to prevent multiple rapid saves
  debouncedSaveAssessment();
});

watch(isPrototype, (newValue) => {
  if (isUpdatingFromStore.value || !blockCreatorStore.currentAssessment) {
    return;
  }

  console.log('Prototype status changed by user:', newValue);
  // Ensure we're setting a boolean value
  blockCreatorStore.currentAssessment.isPrototype = Boolean(newValue);
  debouncedSaveAssessment();
});

// Toggle submission limit
const limitOneSubmission = computed({
  get: () => {
    return subLimit.value === 1;
  },
  set: (value: boolean) => {
    subLimit.value = value ? 1 : -1;
  },
});

// Debounced save function to prevent multiple rapid saves
const debouncedSaveAssessment = () => {
  // Clear existing timeout
  if (saveTimeout.value) {
    clearTimeout(saveTimeout.value);
  }

  // Set new timeout for debounced save
  saveTimeout.value = window.setTimeout(() => {
    void saveAssessment();
  }, 500); // 500ms debounce delay
};

// Function to save the assessment
const saveAssessment = async () => {
  if (blockCreatorStore.currentAssessment) {
    try {
      console.log('💾 Saving assessment...');
      globalStore.startSaveOperation('Saving...');
      // Set flag to prevent circular updates during save
      isUpdatingFromStore.value = true;

      // Create a copy of the assessment to ensure all properties are included
      const assessmentToSave = { ...blockCreatorStore.currentAssessment };

      // Ensure all required fields are set with proper types
      if (startDate.value) {
        assessmentToSave.startAt = startDate.value;
      } else {
        delete assessmentToSave.startAt;
      }

      if (endDate.value) {
        assessmentToSave.endAt = endDate.value;
      } else {
        delete assessmentToSave.endAt;
      }
      assessmentToSave.submitLimit = Number(subLimit.value);
      assessmentToSave.responseEdit = Boolean(responseEdit.value);
      assessmentToSave.globalIsRequired = Boolean(globalIsRequired.value);
      assessmentToSave.isPrototype = Boolean(isPrototype.value);

      await blockCreatorStore.updateAssessment(assessmentToSave.id, assessmentToSave);
      console.log('✅ Assessment saved successfully');
      globalStore.completeSaveOperation(true, 'Saved successfully');
      // Reset flag after save completes
      void nextTick(() => {
        isUpdatingFromStore.value = false;
      });
    } catch (error) {
      console.error('❌ Error saving assessment:', error);
      // Reset flag even on error
      isUpdatingFromStore.value = false;
      globalStore.completeSaveOperation(false, 'Saved successfully');
    }
  } else {
    console.warn('⚠️ No assessment to save');
    globalStore.completeSaveOperation(false, 'Saved successfully');
  }
};

// Handle keep-alive activation
onActivated(() => {
  console.log('SettingEvaluate component activated');
  // Refresh data from store if needed
  if (blockCreatorStore.currentAssessment) {
    console.log('Refreshing settings from store on activation');

    // Set flag to prevent watchers from triggering during activation
    isUpdatingFromStore.value = true;

    startDate.value = blockCreatorStore.currentAssessment.startAt || '';
    endDate.value = blockCreatorStore.currentAssessment.endAt || '';
    responseEdit.value = blockCreatorStore.currentAssessment.responseEdit || false;
    subLimit.value =
      blockCreatorStore.currentAssessment.submitLimit !== undefined
        ? blockCreatorStore.currentAssessment.submitLimit
        : null;
    // Calculate the actual global isRequired state based on current itemBlocks
    const actualGlobalIsRequired = calculateActualGlobalIsRequired();
    globalIsRequired.value = actualGlobalIsRequired;

    // Update the stored value if it doesn't match the actual state
    if (blockCreatorStore.currentAssessment.globalIsRequired !== actualGlobalIsRequired) {
      blockCreatorStore.currentAssessment.globalIsRequired = actualGlobalIsRequired;
      console.log(`🔄 Corrected globalIsRequired on activation: ${actualGlobalIsRequired}`);
    }

    // Reset flag after activation updates
    void nextTick(() => {
      isUpdatingFromStore.value = false;
    });
  }
});

// Handle keep-alive deactivation
onDeactivated(() => {
  console.log('SettingEvaluate component deactivated, saving assessment');

  // Clear any pending debounced save and save immediately
  if (saveTimeout.value) {
    clearTimeout(saveTimeout.value);
    saveTimeout.value = null;
  }

  // Save changes to store immediately on deactivation
  void saveAssessment();
});
</script>

<template>
  <q-page class="q-pa-md">
    <q-card class="evaluate-item" style="border-radius: 10px; max-height: 100vh; overflow-y: auto">
      <!-- Header -->
      <q-card-section class="row items-center justify-between">
        <div class="text-h5 text-weight-bold">ตั้งค่า</div>
        <!-- <q-btn flat dense icon="close" size="lg" @click="router.back()" /> -->
      </q-card-section>

      <q-separator class="q-mt-md" />

      <q-card-section class="column q-gutter-md">
        <div class="row items-start justify-between">
          <div>
            <div class="text-h5 text-weight-bold">กำหนดขอบเขตเวลา</div>
            <div class="text-grey text-h10">กำหนดวันที่เพื่อเปิด-ปิดแบบสอบถามอัตโนมัติ</div>
          </div>

          <div class="row q-gutter-sm">
            <SelectDate
              :model-value="startDate"
              @update:model-value="startDate = $event"
              label="วันที่เริ่มต้น"
            />
            <SelectDate
              :model-value="endDate"
              @update:model-value="endDate = $event"
              label="วันที่สิ้นสุด"
              :disable="startDate === '' || startDate === null"
              :rules="[
                (val: any) => {
                  return val >= startDate || 'วันที่สิ้นสุดต้องมากกว่าหรือเท่ากับวันที่เริ่มต้น';
                },
              ]"
            />
          </div>
        </div>
        <q-separator class="q-mt-md" />

        <!-- Responses -->
        <q-expansion-item
          :model-value="responseEdit || limitOneSubmission"
          header-class="q-pa-none justify-start"
        >
          <template #header>
            <q-item-section>
              <div class="text-h5 text-weight-bold">การตอบกลับ</div>
              <div class="text-grey text-h10">กำหนดวิธีการตอบกลับของผู้ตอบ</div>
            </q-item-section>
          </template>
          <div class="column">
            <div class="sub-row">
              <div>
                <div class="text-h6">สามารถแก้ไขคำตอบ</div>
              </div>
              <Toggle v-model:model-value="responseEdit" />
            </div>
            <div class="sub-row">
              <div>
                <div class="text-h6">จำกัดการส่งได้แค่ครั้งเดียว</div>
              </div>
              <Toggle v-model:model-value="limitOneSubmission" />
            </div>
          </div>
        </q-expansion-item>

        <q-separator class="q-mt-md" />

        <!-- Default Questions -->
        <q-expansion-item :model-value="globalIsRequired" header-class="q-pa-none justify-start">
          <template #header>
            <q-item-section>
              <div>
                <div class="text-h5 text-weight-bold">ค่าตั้งต้นของคำถาม</div>
                <div class="text-grey text-h10">กำหนดค่าตั้งต้นของคำถามทั้งหมดของแบบสอบถามนี้</div>
              </div>
            </q-item-section>
          </template>
          <div class="column">
            <div class="sub-row">
              <div>
                <div class="text-h6">จำเป็นต้องตอบทุกข้อ</div>
              </div>
              <Toggle v-model:model-value="globalIsRequired" />
            </div>
          </div>
        </q-expansion-item>
        <q-separator class="q-mt-md" />
        <div class="row items-start justify-between">
          <div>
            <div class="text-h5 text-weight-bold">เป็นต้นแบบ</div>
            <div class="text-grey text-h10">กำหนดให้ฟอร์มนี้เป็นต้นแบบสำหรับสำเนาเท่านั้น</div>
          </div>
          <div class="q-mt-md">
            <Toggle v-model:model-value="isPrototype" />
          </div>
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>
<style scoped>
.sub-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px; /* q-mb-md */
  margin-top: 16px; /* q-mt-md */
  margin-left: 24px; /* q-ml-lg */
}
</style>
