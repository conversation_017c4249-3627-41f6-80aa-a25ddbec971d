import type { RouteRecordRaw } from 'vue-router';

const umsRoutes: RouteRecordRaw[] = [
  {
    path: '/ums',
    name: 'ums',
    component: () => import('../../layouts/AsmLayout.vue'),
    beforeEnter: (to, from, next) => {
      if (to.path === '/ums') {
        next({ name: 'ums-management' });
      } else {
        next();
      }
    },
    children: [
      {
        path: 'management',
        name: 'ums-management',
        component: () => import('../../pages/ums/UmsMainPage.vue'),
      },
    ],
  },
];

export default umsRoutes;
