/* eslint-disable @typescript-eslint/no-unused-vars */
import { api as axios } from 'src/boot/axios';
import { Notify } from 'quasar';
import type { Submission, Response, getQuestionMeta } from 'src/types/models';
import type {
  QuizAllResponsesData,
  QuizMetaResponse,
  QuizScore,
  SaveQuizResponseRequest,
  StartQuizRequest,
  QuizHeaderWithSubmissions,
} from 'src/types/quiz';

export class QuizService {
  private path = '/submissions';
  private responsePath = '/responses';
  private itemBlockPath = '/item-blocks';

  async getMetaResponse(quizId: number): Promise<QuizMetaResponse> {
    try {
      const response = await axios.get<QuizMetaResponse>(`/quiz/assessments/${quizId}/meta`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching quiz meta for quizId ${quizId}:`, error);
      throw error;
    }
  }

  async getAllResponses(quizId: number): Promise<QuizAllResponsesData> {
    try {
      const response = await axios.get<QuizAllResponsesData>(
        `/quiz/assessments/${quizId}/response`,
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching all responses for quizId ${quizId}:`, error);
      throw error;
    }
  }

  async startQuiz(request: StartQuizRequest): Promise<Submission> {
    try {
      const res = await axios.post<Submission>(`${this.path}/start-assessment`, request);
      return res.data;
    } catch (error) {
      Notify.create({
        message: 'ไม่สามารถเริ่มทำแบบทดสอบได้',
        type: 'negative',
        position: 'bottom',
        timeout: 3000,
      });
      throw new Error('Start quiz failed');
    }
  }

  async getQuestion(submissionId: number, sequence: number): Promise<getQuestionMeta> {
    try {
      const res = await axios.get<getQuestionMeta>(
        `${this.itemBlockPath}/quiz/sequence/${submissionId}/${sequence}`,
      );
      return res.data;
    } catch (error) {
      Notify.create({
        message: 'ไม่สามารถโหลดคำถามได้',
        type: 'negative',
        position: 'bottom',
        timeout: 3000,
      });
      throw new Error('Get question failed');
    }
  }

  async saveQuizResponse(request: SaveQuizResponseRequest): Promise<Response> {
    try {
      const res = await axios.post<Response>(`${this.responsePath}/quiz/save-response`, request);

      return res.data;
    } catch (error) {
      Notify.create({
        message: 'ไม่สามารถบันทึกคำตอบได้',
        type: 'negative',
        position: 'bottom',
        timeout: 3000,
      });
      throw new Error('Save quiz response failed');
    }
  }

  async deleteQuizResponse(responseId: number): Promise<void> {
    try {
      await axios.delete(`${this.responsePath}/${responseId}`);
    } catch (error) {
      Notify.create({
        message: 'ไม่สามารถลบคำตอบได้',
        type: 'negative',
        position: 'bottom',
        timeout: 3000,
      });
      throw new Error('Delete quiz response failed');
    }
  }

  async submitAssessment(submissionId: number): Promise<Submission> {
    try {
      const res = await axios.patch<Submission>(`${this.path}/submit-assessment/${submissionId}`);
      Notify.create({
        message: 'ส่งแบบทดสอบเรียบร้อยแล้ว',
        type: 'positive',
        position: 'bottom',
        timeout: 3000,
      });
      return res.data;
    } catch (error) {
      Notify.create({
        message: 'ไม่สามารถส่งแบบทดสอบได้',
        type: 'negative',
        position: 'bottom',
        timeout: 3000,
      });
      throw new Error('Submit assessment failed');
    }
  }

  async getQuizScore(submissionId: number): Promise<QuizScore> {
    try {
      const res = await axios.get<QuizScore>(`${this.path}/quiz/score/${submissionId}`);
      return res.data;
    } catch (error) {
      Notify.create({
        message: 'ไม่สามารถดึงคะแนนได้',
        type: 'negative',
        position: 'bottom',
        timeout: 3000,
      });
      throw new Error('Get quiz score failed');
    }
  }

  async getQuizHeaderWithSubmissions(
    linkUrl: string,
    userId?: number,
  ): Promise<QuizHeaderWithSubmissions> {
    try {
      const params = userId ? { userId } : {};
      const res = await axios.get<QuizHeaderWithSubmissions>(
        `/assessments/header-with-submissions/url/${linkUrl}`,
        { params },
      );
      return res.data;
    } catch (error) {
      Notify.create({
        message: 'ไม่สามารถดึงข้อมูลแบบทดสอบได้',
        type: 'negative',
        position: 'bottom',
        timeout: 3000,
      });
      throw new Error('Get quiz header with submissions failed');
    }
  }
}

export const quizService = new QuizService();
