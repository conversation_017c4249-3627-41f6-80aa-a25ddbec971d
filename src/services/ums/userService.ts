import type { QTableProps } from 'quasar';
import { api } from 'src/boot/axios';
import type { DataResponse } from 'src/types/data';
import type { User } from 'src/types/models';
import { formatParams } from 'src/utils/utils';

export class UserService {
  private static path = 'users';

  static createUser(dto: User) {
    return api.post<User>(`${this.path}`, dto);
  }

  static getUserById(id: number) {
    return api.get<User>(`${this.path}/${id}`);
  }

  static updateUser(id: number, data: Partial<User>) {
    return api.patch<User>(`${this.path}/${id}`, data);
  }

  static deleteUser(id: number) {
    return api.delete<User>(`${this.path}/${id}`);
  }

  static getUsers(pagination: QTableProps['pagination']) {
    const params = formatParams(pagination);
    return api.get<DataResponse<User>>(`${this.path}`, { params });
  }
}
