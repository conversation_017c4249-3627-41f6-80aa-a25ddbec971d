import { api } from 'src/boot/axios';
import { Notify } from 'quasar';
import type { ImageBody } from 'src/types/models'; // ปรับ path ตามโปรเจกต์คุณ

export class ImageBodyService {
  private path = 'image-bodies';

  async createImageBody(params: Partial<ImageBody>, file: File): Promise<ImageBody> {
    try {
      const formData = this.toFormData(params, file);
      const response = await api.post<ImageBody>(`${this.path}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      Notify.create({ message: 'เพิ่มรูปภาพเรียบร้อยแล้ว', type: 'positive' });
      return response.data;
    } catch {
      Notify.create({ message: 'เพิ่มรูปภาพล้มเหลว', type: 'negative' });
      throw new Error('Create image body failed');
    }
  }

  async getAllImageBodies(): Promise<ImageBody[]> {
    try {
      const response = await api.get<ImageBody[]>(`${this.path}`);
      return response.data;
    } catch {
      throw new Error('Fetch image bodies failed');
    }
  }

  async getImageBodyById(id: number): Promise<ImageBody> {
    try {
      const response = await api.get<ImageBody>(`${this.path}/${id}`);
      return response.data;
    } catch {
      throw new Error('Fetch image body failed');
    }
  }

  async getImageBodyByItemBlockId(itemBlockId: number): Promise<ImageBody | null> {
    try {
      // Get all image bodies and find the one with matching itemBlockId
      const response = await api.get<ImageBody[]>(`${this.path}`);
      const imageBody = response.data.find((body) => body.itemBlockId === itemBlockId);
      return imageBody || null;
    } catch {
      throw new Error('Fetch image body by itemBlockId failed');
    }
  }

  async updateImageBody(id: number, params: Partial<ImageBody>, file?: File): Promise<ImageBody> {
    try {
      const formData = this.toFormData(params, file);
      const response = await api.patch<ImageBody>(`${this.path}/${id}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      Notify.create({ message: 'อัปเดตรูปภาพเรียบร้อยแล้ว', type: 'positive' });
      return response.data;
    } catch {
      Notify.create({ message: 'อัปเดตรูปภาพล้มเหลว', type: 'negative' });
      throw new Error('Update image body failed');
    }
  }

  async updateImageTextOnly(
    id: number,
    imageText: string,
    existingImagePath?: string,
  ): Promise<ImageBody> {
    try {
      // Use multipart/form-data and preserve existing imagePath to prevent deletion
      const formData = new FormData();
      formData.append('imageText', imageText);

      // CRITICAL: Send the existing imagePath to prevent the backend from deleting the image
      // The backend deletes images when imagePath is null/undefined and an image exists
      if (existingImagePath) {
        formData.append('imagePath', existingImagePath);
      }

      const response = await api.patch<ImageBody>(`${this.path}/${id}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      Notify.create({ message: 'อัปเดตข้อความเรียบร้อยแล้ว', type: 'positive' });
      return response.data;
    } catch {
      Notify.create({ message: 'อัปเดตข้อความล้มเหลว', type: 'negative' });
      throw new Error('Update image text failed');
    }
  }

  async removeImageBody(id: number): Promise<void> {
    try {
      await api.delete(`${this.path}/${id}`);
      Notify.create({ message: 'ลบรูปภาพเรียบร้อยแล้ว', type: 'positive' });
    } catch {
      Notify.create({ message: 'ลบรูปภาพล้มเหลว', type: 'negative' });
      throw new Error('Remove image body failed');
    }
  }

  //   private toFormData(data: Record<string, unknown>, file?: File): FormData {
  //     const formData = new FormData();

  //     Object.entries(data).forEach(([key, value]) => {
  //       if (value !== undefined && value !== null) {
  //         if (typeof value === 'object' && value !== null && !(value instanceof File)) {
  //           formData.append(key, JSON.stringify(value));
  //         } else if (
  //           typeof value === 'string' ||
  //           typeof value === 'number' ||
  //           typeof value === 'boolean'
  //         ) {
  //           formData.append(key, String(value));
  //         } else {
  //           formData.append(key, JSON.stringify(value));
  //         }
  //       }
  //     });

  //     if (file) {
  //       formData.append('file', file);
  //     }

  //     return formData;
  //   }
  // }
  private toFormData(data: Record<string, unknown>, file?: File): FormData {
    const formData = new FormData();

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (['imageWidth', 'imageHeight', 'itemBlockId'].includes(key)) {
          formData.append(key, JSON.stringify(value));
        } else if (key === 'imageText') {
          // Send imageText as plain string, not JSON.stringify
          formData.append('imageText', value as string);
        }
      }
    });

    if (file) {
      formData.append('imagePath', file); // ✅ ใช้ key 'imagePath' ตาม API
    }

    return formData;
  }
}
