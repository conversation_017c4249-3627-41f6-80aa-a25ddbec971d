describe('Main Menu Screen', () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let user: any;
  beforeEach(() => {
    cy.fixture('users').then((data) => {
      user = data;
    });
  });
  it('TC-01-01-01 (super admin)', () => {
    cy.login(user.superAdmin.username, user.superAdmin.password);
    cy.url().should('include', '/home');
    cy.get('.text-white > :nth-child(2)').should('contain', 'Super Admin');
  });
  it('TC-01-01-02 (administrator)', () => {
    cy.login(user.admin.username, user.admin.password);
    cy.url().should('include', '/home');
    cy.get('.text-white > :nth-child(2)').should('contain', 'Administrator');
  });
  it('TC-01-01-03 (manager)', () => {
    cy.login(user.manager.username, user.manager.password);
    cy.url().should('include', '/home');
    cy.get('.text-white > :nth-child(2)').should('contain', 'Manager');
  });
  it('TC-01-01-04 (editor)', () => {
    cy.login(user.editor.username, user.editor.password);
    cy.url().should('include', '/home');
    cy.get('.text-white > :nth-child(2)').should('contain', 'Editor');
  });
  it('TC-01-01-05 (standard user)', () => {
    cy.login(user.standardUser.username, user.standardUser.password);
    cy.url().should('include', '/home');
    cy.get('.text-white > :nth-child(2)').should('contain', 'Standard User');
  });
});
