describe('Login Screen', () => {
  it('TC-01-01-01 login success', () => {
    cy.login('superadmin', '1234');
    cy.url().should('include', '/home');
  });
  it('TC-01-01-02 wrong username', () => {
    cy.login('superad', '1234');
    cy.get('.q-notification').should('be.visible');
    cy.get('.q-notification').should('contain', 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง');
  });
  it('TC-01-01-03 wrong password', () => {
    cy.login('superadmin', '1');
    cy.get('.q-notification').should('be.visible');
    cy.get('.q-notification').should('contain', 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง');
  });
  it('TC-01-01-04 logout', () => {
    cy.login('superadmin', '1234');
    cy.url().should('include', '/home');
    cy.logout();
    cy.url().should('include', '/login');
  });
});
